"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReasonConfig = void 0;
const typeorm_1 = require("typeorm");
const base_1 = require("../../framework/model/base");
const constant_1 = require("./constant");
const reason_entity_1 = require("./reason.entity");
let ReasonConfig = class ReasonConfig extends base_1.NumberIdObject {
    reason;
    ownerId;
    ownerName;
    ownerEmail;
    ownerDepartment;
    state;
    stateIdx;
    copy;
};
exports.ReasonConfig = ReasonConfig;
__decorate([
    (0, typeorm_1.ManyToOne)(() => reason_entity_1.Reason, reason => reason.configs, {
        nullable: true,
        onDelete: "CASCADE",
    }),
    __metadata("design:type", reason_entity_1.Reason)
], ReasonConfig.prototype, "reason", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: "人员ID" }),
    __metadata("design:type", Number)
], ReasonConfig.prototype, "ownerId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: "人员姓名" }),
    __metadata("design:type", String)
], ReasonConfig.prototype, "ownerName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "人员邮箱" }),
    __metadata("design:type", String)
], ReasonConfig.prototype, "ownerEmail", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "部门" }),
    __metadata("design:type", String)
], ReasonConfig.prototype, "ownerDepartment", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: constant_1.NodeState,
        nullable: true,
        comment: "节点",
    }),
    __metadata("design:type", String)
], ReasonConfig.prototype, "state", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Number)
], ReasonConfig.prototype, "stateIdx", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        nullable: true,
        enum: ["ANALYZE", "VALIDATE"],
        comment: "抄送人员节点",
    }),
    __metadata("design:type", String)
], ReasonConfig.prototype, "copy", void 0);
exports.ReasonConfig = ReasonConfig = __decorate([
    (0, typeorm_1.Entity)()
], ReasonConfig);
//# sourceMappingURL=reason-config.entity.js.map