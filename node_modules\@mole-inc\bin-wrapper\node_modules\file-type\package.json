{"name": "file-type", "version": "17.1.6", "description": "Detect the file type of a Buffer/Uint8Array/ArrayBuffer", "license": "MIT", "repository": "sindresorhus/file-type", "funding": "https://github.com/sindresorhus/file-type?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {".": {"node": "./index.js", "default": "./browser.js"}, "./core": "./core.js"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "browser.js", "browser.d.ts", "core.js", "core.d.ts", "supported.js", "util.js"], "keywords": ["mime", "file", "type", "magic", "archive", "image", "img", "pic", "picture", "flash", "photo", "video", "detect", "check", "is", "exif", "elf", "exe", "binary", "buffer", "uint8array", "jpg", "png", "apng", "gif", "webp", "flif", "xcf", "cr2", "cr3", "orf", "arw", "dng", "nef", "rw2", "raf", "tif", "bmp", "icns", "jxr", "psd", "indd", "zip", "tar", "rar", "gz", "bz2", "7z", "dmg", "mp4", "mid", "mkv", "webm", "mov", "avi", "mpg", "mp2", "mp3", "m4a", "ogg", "opus", "flac", "wav", "amr", "pdf", "epub", "mobi", "swf", "rtf", "woff", "woff2", "eot", "ttf", "otf", "ico", "flv", "ps", "xz", "sqlite", "xpi", "cab", "deb", "ar", "rpm", "Z", "lz", "cfb", "mxf", "mts", "wasm", "webassembly", "blend", "bpg", "docx", "pptx", "xlsx", "3gp", "jp2", "jpm", "jpx", "mj2", "aif", "odt", "ods", "odp", "xml", "heic", "ics", "glb", "pcap", "dsf", "lnk", "alias", "voc", "ac3", "3g2", "m4b", "m4p", "m4v", "f4a", "f4b", "f4p", "f4v", "mie", "qcp", "asf", "ogv", "ogm", "oga", "spx", "ogx", "ape", "wv", "cur", "nes", "crx", "ktx", "dcm", "mpc", "arrow", "shp", "aac", "mp1", "it", "s3m", "xm", "ai", "skp", "avif", "eps", "lzh", "pgp", "asar", "stl", "chm", "3mf", "zst", "jxl", "vcf"], "dependencies": {"readable-web-to-node-stream": "^3.0.2", "strtok3": "^7.0.0-alpha.9", "token-types": "^5.0.0-alpha.2"}, "devDependencies": {"@tokenizer/token": "^0.3.0", "@types/node": "^16.11.10", "ava": "^3.15.0", "commonmark": "^0.30.0", "noop-stream": "^1.0.0", "tsd": "^0.19.0", "typescript": "^4.5.2", "xo": "^0.46.4"}, "xo": {"envs": ["node", "browser"], "rules": {"no-inner-declarations": "warn", "no-await-in-loop": "warn", "no-bitwise": "off", "@typescript-eslint/no-unsafe-assignment": "off"}}, "ava": {"serial": true, "verbose": true}}