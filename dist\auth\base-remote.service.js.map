{"version": 3, "file": "base-remote.service.js", "sourceRoot": "", "sources": ["../../src/auth/base-remote.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yDAA4C;AAC5C,oDAA8C;AAE9C,MAAsB,iBAAiB;IACrC,KAAK,CAAC,OAAO,CACX,GAAW,EACX,MAAc,EACd,KAAc,EACd,IAA0B,EAC1B,MAA+B;QAE/B,MAAM,SAAS,GAAG,IAAI,oBAAO,EAAE,CAAC;QAChC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,CAAC;YACpB,SAAS,CAAC,MAAM,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC,CAAC;QACvD,CAAC;QACD,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;YACb,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBACtC,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QACD,MAAM,MAAM,GAAwB;YAClC,MAAM;YACN,OAAO,EAAE,SAAS;YAClB,QAAQ,EAAE,QAAQ;SACnB,CAAC;QACF,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;YACX,SAAS,CAAC,MAAM,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;YACrD,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC;QACD,MAAM,GAAG,GAAG,MAAM,IAAA,oBAAK,EAAC,GAAG,mBAAM,CAAC,OAAO,GAAG,GAAG,EAAE,EAAE,MAAa,CAAC,CAAC;QAClE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;QAC3C,OAAO,OAAO,CAAC,CAAC,CAAE,IAAU,CAAC,CAAC,CAAC,IAAI,CAAC;IACtC,CAAC;CACF;AA9BD,8CA8BC"}