{"version": 3, "file": "puppeteer-core.module.js", "sourceRoot": "", "sources": ["../../src/puppeteer/puppeteer-core.module.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CASwB;AACxB,uCAAyC;AACzC,mDAAiD;AACjD,qCAA2C;AAM3C,+DAG+B;AAIxB,IAAM,mBAAmB,2BAAzB,MAAM,mBAAmB;IAIX;IACA;IAJF,MAAM,GAAG,IAAI,eAAM,CAAC,iBAAiB,CAAC,CAAC;IACxD,YAEmB,OAA+B,EAC/B,SAAoB;QADpB,YAAO,GAAP,OAAO,CAAwB;QAC/B,cAAS,GAAT,SAAS,CAAW;IACpC,CAAC;IAEJ,MAAM,CAAC,OAAO,CAAC,UAAe,EAAE;QAC9B,MAAM,sBAAsB,GAAG;YAC7B,OAAO,EAAE,8CAAwB;YACjC,QAAQ,EAAE,OAAO;SAClB,CAAC;QAEF,MAAM,cAAc,GAAG;YACrB,OAAO,EAAE,+CAAyB;YAClC,UAAU,EAAE,KAAK,EAAE,OAA+B,EAAE,EAAE;gBACpD,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,MAAM,EAAE,CAAC,8CAAwB,CAAC;SACnC,CAAC;QAEF,MAAM,eAAe,GAAG;YACtB,OAAO,EAAE,IAAA,wBAAe,EAAC,OAAO,CAAC;YACjC,UAAU,EAAE,KAAK,EAAE,OAA+B,EAAE,EAAE;gBACpD,OAAO,MAAM,IAAA,uBAAM,EAAC,OAAO,CAAC,CAAC;YAC/B,CAAC;YACD,MAAM,EAAE,CAAC,8CAAwB,CAAC;SACnC,CAAC;QAEF,MAAM,SAAS,GAAG,CAAC,sBAAsB,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;QAC5E,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC,CAAC;QAElC,OAAO;YACL,MAAM,EAAE,qBAAmB;YAC3B,SAAS;YACT,OAAO;SACR,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,OAAoC;QACtD,MAAM,cAAc,GAAG;YACrB,OAAO,EAAE,+CAAyB;YAClC,UAAU,EAAE,KAAK,EAAE,OAA+B,EAAE,EAAE;gBACpD,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,MAAM,EAAE,CAAC,8CAAwB,CAAC;SACnC,CAAC;QACF,MAAM,eAAe,GAAG;YACtB,OAAO,EAAE,IAAA,wBAAe,EAAC,OAAO,CAAC;YACjC,UAAU,EAAE,KAAK,EAAE,OAA+B,EAAE,EAAE;gBACpD,OAAO,MAAM,IAAA,uBAAM,EAAC,OAAO,CAAC,CAAC;YAC/B,CAAC;YACD,MAAM,EAAE,CAAC,8CAAwB,CAAC;SACnC,CAAC;QACF,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAE1D,MAAM,SAAS,GAAG,CAAC,GAAG,cAAc,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;QAEvE,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC,CAAC;QAElC,OAAO;YACL,MAAM,EAAE,qBAAmB;YAC3B,SAAS;YACT,OAAO;SACR,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,qBAAqB;QACzB,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAU,IAAA,wBAAe,EAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAE3E,IAAI,CAAC;YACH,IAAI,OAAO,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACjC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;gBACtC,MAAM,OAAO,CAAC,KAAK,EAAE,CAAC;YACxB,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAChC,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,oBAAoB,CACjC,OAAoC;QAEpC,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YAC9C,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC,CAAC;QACpD,CAAC;QAED,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAyC,CAAC;QAEnE,OAAO;YACL,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC;YACxC;gBACE,OAAO,EAAE,QAAQ;gBACjB,QAAQ;aACT;SACF,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,0BAA0B,CACvC,OAAoC;QAEpC,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvB,OAAO;gBACL,OAAO,EAAE,8CAAwB;gBACjC,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,EAAE;aAC7B,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG;YACb,CAAC,OAAO,CAAC,QAAQ;gBACf,OAAO,CAAC,WAAW,CAAkC;SACxD,CAAC;QAEF,OAAO;YACL,OAAO,EAAE,8CAAwB;YACjC,UAAU,EAAE,KAAK,EAAE,cAAuC,EAAE,EAAE,CAC5D,MAAM,cAAc,CAAC,sBAAsB,CAAC,OAAO,CAAC,IAAI,CAAC;YAC3D,MAAM;SACP,CAAC;IACJ,CAAC;CACF,CAAA;AA1HY,kDAAmB;8BAAnB,mBAAmB;IAF/B,IAAA,eAAM,GAAE;IACR,IAAA,eAAM,EAAC,EAAE,CAAC;IAIN,WAAA,IAAA,eAAM,EAAC,8CAAwB,CAAC,CAAA;6CAEL,gBAAS;GAL5B,mBAAmB,CA0H/B"}