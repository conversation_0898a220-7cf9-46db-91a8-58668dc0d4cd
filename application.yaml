datasource:
  - mysql:
      host: *************
      port: 3306
      username: yj
      password: Yjdi787^&d
      database: hertai-car
      logging: ["query", "error", "schema"]
      synchronize: true

#storage:
#  base:
#    dir: /home/<USER>/下载/test

server:
  url: http://*************:3001
  contextPath: admin-api
  loginPage: http://*************:3001/#/login?appCode=car
  carUrl: http://localhost:4000/
#  mailTemplate: /home/<USER>/
  testCopyers: ["<EMAIL>"]
  # name: DFX
  name: CAR

port: 3000
contextPath: /api

mail:
  transport:
    host: smtphz.qiye.163.com
    secure: true
    port: 465
    logger: true
    auth:
      user: <EMAIL>
      pass: GGTTyy09
  defaults:
    from: '"QMS" <<EMAIL>>'
