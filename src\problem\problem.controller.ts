import {
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  HttpStatus,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  Req,
  Res,
  UploadedFile,
  UseInterceptors,
  Optional,
  Inject,
} from "@nestjs/common";
import { FileInterceptor } from "@nestjs/platform-express";
import { Cron } from "@nestjs/schedule";
import dayjs from "dayjs";
import type { Response } from "express";
import { unlinkSync } from "fs";
import { diskStorage, memoryStorage } from "multer";
import { extname, join, parse } from "path";
import PDFDocument from "pdfkit";
import { Browser } from "puppeteer-core";
import type { Payload } from "src/auth/jwt.strategy";
import { Public } from "src/auth/public.decorator";
import { DictionaryService } from "src/dictionary/dictionary.service";
import { DictionaryCategory } from "src/dictionary/entities/dictionary.entity";
import { ProblemDescription } from "src/framework/model/base";
import { BatchIdRequest, QueryRequest } from "src/framework/service/Query";
import { MailService } from "src/mail/mail.service";
import { UserService } from "src/user/user.service";
import Exporter from "src/utils/excel/exportData";
import { ExcelEntityPopulator } from "src/utils/excel/importData";
import { imageFileFilter } from "src/utils/file-upload.utils";
import { Equal, In } from "typeorm";
import { v4 as uuidv4 } from "uuid";
import { CodeRuleService } from "./../code-rule/code-rule.service";
import { config } from "./../utils/properties";
import { CreateReasonDto } from "./dto/create-reason.dto";
import {
  NodeState,
  ProblemStatus,
  ReasonStatus,
  nodes,
} from "./entities/constant";
import { ProblemOperateLog } from "./entities/problem-operate-log.entity";
import { Problem } from "./entities/problem.entity";
import { ReasonConfig } from "./entities/reason-config.entity";
import { ReasonDetail } from "./entities/reason-detail.entity";
import { Reason } from "./entities/reason.entity";
import { ProblemOperateLogService } from "./problem-operate-log.service";
import { ProblemService } from "./problem.service";
import { ReasonConfigService } from "./reason-config.service";
import { ReasonDetailService } from "./reason-detail.service";
import { ReasonService } from "./reason.service";

const ProblemStatusOptions: Record<ProblemStatus, string> = {
  [ProblemStatus.DRAFT]: "草稿",
  [ProblemStatus.CQE]: "待CQE分配",
  [ProblemStatus.NEW]: "待处理",
  [ProblemStatus.PROCESSING]: "处理中",
  [ProblemStatus.CLOSED]: "已关闭",
  [ProblemStatus.OBSOLETE]: "已作废",
};

export const ReasonStateOptions: Record<NodeState, string> = {
  [NodeState.ANALYZE]: "原因分析",
  [NodeState.ANALYZE_AUDIT]: "原因分析审核",
  [NodeState.VALIDATE]: "效果验证",
  [NodeState.VALIDATE_AUDIT]: "效果验证审核",
  [NodeState.CQE_AUDIT]: "CQE经理审核",
  [NodeState.COMPLETE]: "完成",
};

const problemStorage = diskStorage({
  destination: join(config.baseStoreageDir, "problem"),
  filename: (req, file, cb) => {
    const extension = extname(file.originalname);
    const randomName = uuidv4();
    cb(null, `${randomName}${extension}`);
  },
});

export const reasonStorage = diskStorage({
  destination: join(config.baseStoreageDir, "reason"),
  filename: (req, file, cb) => {
    const extension = extname(file.originalname);
    const randomName = uuidv4();
    cb(null, `${randomName}${extension}`);
  },
});

@Controller("problem")
export class ProblemController {
  constructor(
    private readonly service: ProblemService,
    private readonly logService: ProblemOperateLogService,
    private readonly reasonService: ReasonService,
    private readonly reasonConfigService: ReasonConfigService,
    private readonly reasonDetailService: ReasonDetailService,
    private readonly mailService: MailService,
    private readonly codeRuleService: CodeRuleService,
    private readonly dictionaryService: DictionaryService,
    private readonly userService: UserService,
    @Optional() @Inject('Browser') private readonly browser?: Browser,
  ) {}

  async convertImageToPdf(
    imageBuffer: Buffer,
    dimensions: { width: number; height: number },
  ): Promise<Buffer> {
    return new Promise(resolve => {
      const doc = new PDFDocument({ autoFirstPage: false });
      const chunks: Uint8Array[] = [];
      doc.addPage({
        size: [dimensions.width + 30, dimensions.height + 30],
      });

      doc.image(imageBuffer, 15, 15, {
        fit: [dimensions.width, dimensions.height],
      });
      doc.on("data", chunk => {
        chunks.push(chunk);
      });
      doc.on("end", () => {
        const resultBuffer = Buffer.concat(chunks);
        resolve(resultBuffer);
      });
      doc.end();
    });
  }

  @Get("pdf/:id")
  async pdf(
    @Req() req: { payload: Payload },
    @Param("id") id: number,
    @Res() res: Response,
  ) {
    if (!this.browser) {
      throw new InternalServerErrorException('PDF generation service is not available');
    }

    const user = await this.userService.findByToken(req.payload.token);
    const problem = await this.service.get(id);
    const page = await this.browser.newPage();
    await page.setViewport({ width: 1920, height: 9999 });
    /*  await page.goto(`${config.carUrl}?token=1`);
    await page.waitForNavigation(); */
    const token = user.languageCode === "en" ? 2 : 1;
    const file = user.languageCode === "en" ? "Problem_" : "问题";
    try {
      await page.goto(`${config.carUrl}?authKey=${token}&redirect=/pdf/${id}`, {
        waitUntil: "domcontentloaded",
      });
      await page.waitForNavigation({ timeout: 20000 });
      await page.waitForNetworkIdle({ timeout: 20000 });
      // await page.waitForSelector(`#${problem.code}`, { timeout: 10000 });
      await page.content();
      const bodyHandlers = await page.$$("#app > .v-descriptions");
      let _height = 0;
      for (const item of bodyHandlers) {
        const { height } = await item.boundingBox();
        _height += height;
      }

      await page.setViewport({
        width: Math.ceil(1920),
        height: Math.ceil(_height),
        deviceScaleFactor: 2,
      });
      const buffer = await page.screenshot({
        fullPage: true,
      });
      const pdfBuffer = await this.convertImageToPdf(buffer, {
        width: 1920,
        height: _height,
      });
      res.contentType("application/pdf");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename*=${encodeURIComponent(
          file + "[" + problem.code + "].pdf",
        )}`,
      );
      res.send(pdfBuffer);
    } finally {
      await page.close();
    }
  }

  @Post("list")
  async query(@Req() req: { payload: Payload }, @Body() request: QueryRequest) {
    /*  request.params = request.params ?? {};
    request.params["creatorId"] = req.payload.id;
    request.operations = request.operations ?? {};
    request.operations["creatorId"] = "IN"; */
    return await this.service.query(request);
  }

  @Post("export")
  async export(
    @Req() req: { payload: Payload },
    @Body() request: QueryRequest & { ids?: number[] },
    @Res() res: Response,
  ) {
    const langs = await this.userService.getLanguages();
    const user = await this.userService.findByToken(req.payload.token);
    const lang = langs.find(item => item.code === (user.languageCode ?? "zh"));
    const trans = lang.langData.reduce((prev, curr) => {
      prev[curr.label] = curr.value;
      return prev;
    }, {});
    const t = (key: string) => {
      return trans[key] ?? key;
    };
    let problems: Problem[];
    if (Array.isArray(request.ids) && !!request.ids?.length) {
      problems = (await this.service.query({
        params: { id: request.ids },
        operations: { id: "IN" },
      })) as Problem[];
    } else {
      problems = (await this.service.query(request)) as Problem[];
    }
    const file = user.languageCode === "en" ? "Problem" : "问题";
    const exporter = new Exporter();
    const sheet = exporter.addSheet({
      header: [
        { header: t("问题编号"), key: "code" },
        { header: t("创建日期"), key: "createdOn" },
        { header: t("机型"), key: "machineType" },
        { header: t("客户"), key: "customer" },
        { header: t("项目编号"), key: "projectCode" },
        { header: t("工单号"), key: "workOrderCode" },
        { header: t("工单数量"), key: "workOrderNum" },
        { header: t("和而泰工厂"), key: "factory" },
        { header: t("事业部"), key: "businessUnit" },
        { header: t("创建人"), key: "creatorName" },
        { header: t("产品阶段"), key: "productStep" },
        { header: t("问题描述"), key: "descriptions" },
        { header: t("状态"), key: "status" },
        { header: t("当前节点"), key: "node" },
        { header: t("责任人"), key: "responsor" },
        { header: t("下一节点"), key: "nextNode" },
      ],
      data: problems.map(
        ({
          code,
          createdOn,
          machineType,
          customer,
          projectCode,
          workOrderCode,
          workOrderNum,
          factory,
          businessUnit,
          creatorName,
          productStep,
          descriptions,
          status,
          reasons,
        }) => ({
          code,
          createdOn,
          machineType: t(machineType),
          customer: t(customer),
          projectCode,
          workOrderCode,
          workOrderNum,
          factory: t(factory),
          businessUnit: t(businessUnit),
          creatorName,
          productStep,
          descriptions:
            user.languageCode === "en"
              ? `1.What happened:${
                  descriptions?.what ?? ""
                }\n2.Why is it an issue:${
                  descriptions?.why ?? ""
                }\n3.Where was the issue detected:${
                  descriptions?.where ?? ""
                }\n4.When detected:${
                  descriptions?.when ?? ""
                };\n5.Who detected the issue:${
                  descriptions?.who ?? ""
                }\n6.How was the issue detected:${
                  descriptions?.how_detected ?? ""
                }\n7.How many:${descriptions?.how_many ?? ""}`
              : `1.发生了什么:${descriptions?.what ?? ""}\n2.为什么是一个问题:${
                  descriptions?.why ?? ""
                }\n3.在哪里发现这个问题:${
                  descriptions?.where ?? ""
                }\n4.什么时候发现:${
                  descriptions?.when ?? ""
                };\n5.谁发现这个问题:${
                  descriptions?.who ?? ""
                }\n6.怎么发现的这个问题:${
                  descriptions?.how_detected ?? ""
                }\n7.发现数量:${descriptions?.how_many ?? ""}`,
          status: t(ProblemStatusOptions[status]),
          node: reasons
            .map(reason => t(ReasonStateOptions[reason.state]))
            .join("\n"),
          responsor: reasons
            .map(
              reason =>
                reason.configs.find(config => config.state === reason.state)
                  ?.ownerName ?? "-",
            )
            .join("\n"),
          nextNode: reasons
            .map(reason => {
              const current = reason.configs.find(
                config => config.state === reason.state,
              );
              const next = reason.configs.find(
                config => config.stateIdx === (current?.stateIdx ?? -999) + 1,
              );
              return `${
                !!next && !!next.state ? t(ReasonStateOptions[next.state]) : "-"
              }${
                !!next && !!next.ownerName?.length
                  ? "[" + next.ownerName + "]"
                  : ""
              }`;
            })
            .join("\n"),
        }),
      ),
    });
    sheet.columns.forEach(column => {
      if (
        column.key === "descriptions" ||
        column.key === "node" ||
        column.key === "responsor" ||
        column.key === "nextNode"
      ) {
        column.eachCell((cell, idx) => {
          if (idx > 1) {
            cell.style = {
              ...cell.style,
              alignment: {
                ...cell.style?.alignment,
                horizontal: column.key === "descriptions" ? "left" : "center",
                wrapText: true,
              },
            };
          }
        });
      }
    });
    exporter.doExport({
      filename: file + "_" + dayjs().format("YYYYMMDDHHmmss") + ".xlsx",
      response: res,
    });
  }

  @Get(":id")
  async info(@Param("id") id: number) {
    return await this.service.get(id);
  }

  @Post("/")
  async save(
    @Req() request: { payload: Payload },
    @Body() entity: Partial<Problem>,
  ) {
    if (!entity.id) {
      entity.creatorId = request.payload.id;
      entity.creatorName = request.payload.name;
    } else {
      const problem = await this.service.get(entity.id);
      if (
        problem.creatorId !== request.payload.id &&
        problem.cqeId !== request.payload.id
      ) {
        throw new ForbiddenException("没有权限修改问题");
      }
    }
    return await this.service.saveOrUpdate(entity);
  }

  @Post("/submit/:id")
  async submit(@Req() request: { payload: Payload }, @Param("id") id: number) {
    const problem = await this.service.get(id);
    if (problem.status === ProblemStatus.DRAFT) {
      if (problem.creatorId !== request.payload.id) {
        throw new ForbiddenException("只可以提交自己创建的问题");
      }
      problem.status = ProblemStatus.CQE;
      const result = await this.service.execute(async manager => {
        await this.codeRuleService.genCode(problem, this.service.repository);
        try {
          await this.userService.addTodo(
            request.payload.token,
            [],
            problem,
            NodeState.ANALYZE,
          );
        } catch {}
        const log = this.logService.repository.create({
          problem: { id },
          operatorId: request.payload.id,
          operatorName: request.payload.name,
          action: {
            key: "problem.action.submit_cqe",
            message: "创建问题并提交CQE分配",
          },
        });
        await manager.save(this.logService.target, log);
        return await manager.save(this.service.target, problem);
      });
      return result;
    } else if (problem.status === ProblemStatus.CQE) {
      if (problem.cqeId !== request.payload.id) {
        throw new ForbiddenException("不是指定的CQE,无法提交");
      }
      problem.status = ProblemStatus.NEW;
      const result = await this.service.execute(async manager => {
        const log = this.logService.repository.create({
          problem: { id },
          operatorId: request.payload.id,
          operatorName: request.payload.name,
          action: { key: "problem.action.submit", message: "提交了问题" },
        });
        await manager.save(this.logService.target, log);
        return await manager.save(this.service.target, problem);
      });
      const reasons = await this.reasonService.findByProblemId(result.id);
      try {
        if (!!result.todoId) {
          await this.userService.processedTodo(request.payload.token, [
            result.todoId,
          ]);
        }
      } catch {}

      const results = await this.userService.addTodo(
        request.payload.token,
        reasons,
        problem,
        NodeState.ANALYZE,
      );
      if (results?.length) {
        await this.reasonService.execute(async manager => {
          return manager.save(this.reasonService.target, results);
        });
      }
      this.mailService.sendEmail(reasons, NodeState.ANALYZE, problem);
      return result;
    } else {
      throw new ForbiddenException("当前状态不可提交");
    }
  }

  @Delete("/")
  async remove(
    @Req() req: { payload: Payload },
    @Body() request: BatchIdRequest,
  ) {
    const user = await this.userService.findByToken(req.payload.token);
    const isAdmin = (user.roleId ?? []).includes("1");
    const conditions: Record<string, any> = { id: In(request.ids) };
    if (!isAdmin) {
      conditions.creatorId = Equal(req.payload.id);
    }
    const entities = await this.service.repository.findBy(conditions);
    await this.service.deleteEntities(entities, isAdmin, async toObsolete => {
      const todoIds = new Set<number>();
      for (const p of toObsolete) {
        const reasons = await this.reasonService.findByProblemId(p.id);
        reasons
          .filter(reason => !!reason.todoId)
          .forEach(reason => todoIds.add(reason.todoId));
      }
      if (todoIds.size > 0) {
        await this.userService.processedTodo(
          req.payload.token,
          Array.from(todoIds),
          "2",
        );
      }
    });
  }

  @Post("upload/:id/:type")
  @UseInterceptors(
    FileInterceptor("file", {
      storage: problemStorage,
      fileFilter: imageFileFilter,
    }),
  )
  async uploadFile(
    @Param("id") id: number,
    @Param("type") type: "good" | "bad",
    @UploadedFile() file: Express.Multer.File,
  ) {
    const problem = await this.service.get(id);
    if (!!file) {
      let attachments;
      if (type === "good") {
        problem.goodPartImages = problem.goodPartImages ?? [];
        attachments = problem.goodPartImages;
      } else {
        problem.badPartImages = problem.badPartImages ?? [];
        attachments = problem.badPartImages;
      }
      attachments.push({
        bucket: file.destination,
        extension: extname(file.originalname),
        filename: file.originalname,
        key: parse(file.filename).name,
        size: file.size,
        type: file.mimetype,
      });
      return await this.service.execute(manager =>
        manager.save(Problem, problem),
      );
    }
    throw new InternalServerErrorException("File upload failed");
  }

  @Delete("attachment/:type/:id/:key")
  async removeFile(
    @Param("type") type: "good" | "bad",
    @Param("id") id: number,
    @Param("key") key: string,
  ) {
    const entity = await this.service.get(id);
    const attachments =
      (type === "good" ? entity.goodPartImages : entity.badPartImages) ?? [];
    const idx = attachments.findIndex(item => item.key === key);
    if (idx > -1) {
      const [attachment] = attachments.splice(idx, 1);
      if (!!attachment) {
        try {
          unlinkSync(
            join(attachment.bucket, `${attachment.key}${attachment.extension}`),
          );
        } catch {}
      }
    }
    return await this.service.execute(manager =>
      manager.save(this.service.target, entity),
    );
  }

  @Public()
  @Get("attachment/:type/:id/:key")
  async attachment(
    @Param("type") type: "good" | "bad",
    @Param("id") id: number,
    @Param("key") key: string,
    @Res() res: Response,
  ) {
    const problem = await this.service.get(id);
    const attachments =
      (type === "good" ? problem.goodPartImages : problem.badPartImages) ?? [];
    const attachment = attachments.find(item => item.key === key);
    if (!attachment) {
      throw new InternalServerErrorException("没有找到附件");
    }
    res.sendFile(
      join(attachment.bucket, `${attachment.key}${attachment.extension}`),
      {
        headers: {
          "Content-Disposition": `attachment; filename*=${encodeURIComponent(
            attachment.filename,
          )}`,
        },
      },
    );
  }

  @Get("logs/:id")
  async logs(@Param("id") id: number): Promise<ProblemOperateLog[]> {
    return this.logService.findByProblemId(id);
  }

  @Get(":id/reason")
  async reasons(@Param("id") id: number): Promise<Reason[]> {
    return this.reasonService.findByProblemId(id);
  }

  @Post(":id/reason")
  async addOrModifyReason(
    @Req() request: { payload: Payload },
    @Param("id") id: number,
    @Body() entity: CreateReasonDto,
  ): Promise<Reason> {
    const problem = await this.service.get(id);
    if (
      problem.status === ProblemStatus.CLOSED ||
      problem.status === ProblemStatus.OBSOLETE
    ) {
      throw new ForbiddenException("问题已关闭,非法操作");
    }
    const isNew = !entity.id && problem.status !== ProblemStatus.CQE;
    const target = !!entity.id
      ? await this.reasonService.get(entity.id)
      : this.reasonService.repository.create({
          ...entity,
          problem: { id },
        });
    const reason = await this.reasonService.execute(async manager => {
      target.category = entity.category;
      target.subCategory = entity.subCategory;
      target.configs = [
        ...entity.configs.map(config => ({
          ...config,
          stateIdx:
            nodes.find(node => node.state === config.state)?.index ?? -1,
        })),
      ];
      return manager.save(this.reasonService.target, target);
    });
    if (isNew) {
      const results = await this.userService.addTodo(
        request.payload.token,
        [reason],
        problem,
        NodeState.ANALYZE,
      );
      if (results?.length) {
        await this.reasonService.execute(async manager => {
          return manager.save(this.reasonService.target, results);
        });
      }
      this.mailService.sendEmail([reason], NodeState.ANALYZE, problem);
    }
    return reason;
  }

  @Delete(":id/reason")
  async deleteReason(
    @Req() req: { payload: Payload },
    @Param("id") id: number,
    @Body() request: BatchIdRequest,
  ): Promise<void> {
    const problem = await this.service.get(id);
    const reasons = await this.reasonService.findByProblemId(id);
    if (problem.status === ProblemStatus.DRAFT) {
      await this.reasonService.delete(
        reasons
          .filter(reason => request.ids.indexOf(reason.id) > -1)
          .map(reason => reason.id),
      );
    } else if (problem.status === ProblemStatus.CLOSED) {
      throw new ForbiddenException("问题已关闭,非法操作");
    } else {
      await this.reasonService.execute(async manager => {
        const toDelete = reasons.filter(
          reason => request.ids.indexOf(reason.id) > -1,
        );
        const logs = [];
        toDelete.forEach(item => {
          item.delete = true;
          item.deleteById = req.payload.id;
          item.deleteByName = req.payload.name;
          const log = this.logService.repository.create({
            problem: { id },
            operatorId: req.payload.id,
            operatorName: req.payload.name,
            action: {
              key: "problem.action.obsolete_reason",
              message: "作废了原因",
            },
          });
          logs.push(log);
        });
        const todoIds = toDelete
          .filter(item => !!item.todoId)
          .map(item => item.todoId);
        if (!!todoIds.length) {
          await this.userService.processedTodo(req.payload.token, todoIds, "2");
        }
        manager.save(this.logService.target, logs);
        return manager.save(this.reasonService.target, toDelete);
      });
    }
  }

  @Put(":id/reason")
  async updateReason(
    @Param("id") id: number,
    @Body() entity: Reason,
  ): Promise<Reason> {
    const problem = await this.service.get(id);
    const target = await this.reasonService.get(entity.id);
    if (problem.status === ProblemStatus.CLOSED) {
      throw new ForbiddenException("问题已关闭,非法操作");
    } else if (problem.status === ProblemStatus.DRAFT) {
      return this.reasonService.execute(async manager => {
        target.configs = entity.configs;
        return manager.save(this.reasonService.target, target);
      });
    } else {
      return this.reasonService.saveOrUpdate(entity);
    }
  }

  @Post(":id/reason-detail")
  @UseInterceptors(
    FileInterceptor("file", {
      storage: reasonStorage,
    }),
  )
  async saveOrUpdateReasonDetail(
    @Param("id") id: number,
    @Body() entity: ReasonDetail,
    @UploadedFile() file: Express.Multer.File,
  ) {
    const reason = await this.reasonService.get(id);
    if (
      reason.status === ReasonStatus.CLOSED ||
      reason.status === ReasonStatus.FOLLOW
    ) {
      throw new ForbiddenException("流程正在进行中或已关闭,非法操作");
    }
    if (!!file) {
      entity.attachment = {
        bucket: file.destination,
        extension: extname(file.originalname),
        filename: file.originalname,
        key: parse(file.filename).name,
        size: file.size,
        type: file.mimetype,
      };
    }
    return this.reasonDetailService.saveOrUpdate(entity);
  }

  @Delete(":id/reason-detail")
  async deleteReasonDetail(
    @Param("id") id: number,
    @Body() request: BatchIdRequest,
  ) {
    const reason = await this.reasonService.get(id);
    if (
      reason.status === ReasonStatus.CLOSED ||
      reason.status === ReasonStatus.FOLLOW
    ) {
      throw new ForbiddenException("流程正在进行中或已关闭,非法操作");
    }
    const details = await this.reasonDetailService.findByReasonId(id);
    await this.reasonDetailService.delete(
      details
        .filter(entity => request.ids.indexOf(entity.id) > -1)
        .map(entity => entity.id),
    );
  }

  @Post("sendMails")
  async sendMails(@Req() req: { payload: Payload }) {
    const user = await this.userService.findByToken(req.payload.token);
    const isAdmin = (user.roleId ?? []).includes("1");
    if (isAdmin) {
      const builder = this.reasonService.repository
        .createQueryBuilder("r")
        .innerJoinAndSelect("r.problem", "p")
        .innerJoinAndSelect("r.configs", "config")
        .where("r.delete is false")
        .andWhere("r.state in (:...states)", {
          states: [
            NodeState.ANALYZE,
            NodeState.ANALYZE_AUDIT,
            NodeState.VALIDATE,
            NodeState.VALIDATE_AUDIT,
          ],
        })
        .andWhere(`p.status not in (:...statuses)`, {
          statuses: [
            ProblemStatus.DRAFT,
            ProblemStatus.OBSOLETE,
            ProblemStatus.CLOSED,
          ],
        });
      const reasons = (await this.reasonService.doQuery(
        builder,
        false,
      )) as Reason[];
      const problems: Record<number, Record<NodeState, Reason[]>> = {};
      for (const reason of reasons) {
        problems[reason.problem.id] =
          problems[reason.problem.id] ??
          ({} as unknown as Record<NodeState, Reason[]>);
        problems[reason.problem.id][reason.state] =
          problems[reason.problem.id][reason.state] ?? [];
        problems[reason.problem.id][reason.state].push(reason);
      }
      for (const stateReason of Object.values(problems)) {
        for (const [state, reasons] of Object.entries(stateReason)) {
          await this.mailService.sendEmail(
            reasons,
            state as NodeState,
            reasons[0].problem,
          );
        }
      }
    }
  }

  @Cron("0 0 8 * * *")
  //@Cron("0/30 * * * * *")
  async handleCron() {
    const today = dayjs();
    {
      const builder = this.reasonService.repository
        .createQueryBuilder("r")
        .innerJoinAndSelect("r.problem", "p")
        .innerJoinAndSelect("r.configs", "config")
        .where("r.delete is false")
        .andWhere("r.updatedAt < :deadline", {
          deadline: today.subtract(7, "day").toDate(),
        })
        .andWhere("r.state in (:...states)", {
          states: [
            NodeState.ANALYZE_AUDIT,
            NodeState.VALIDATE_AUDIT,
            NodeState.CQE_AUDIT,
          ],
        })
        .andWhere(`p.status not in (:...statuses)`, {
          statuses: [
            ProblemStatus.DRAFT,
            ProblemStatus.CQE,
            ProblemStatus.OBSOLETE,
            ProblemStatus.CLOSED,
          ],
        });
      const reasons = (await this.reasonService.doQuery(
        builder,
        false,
      )) as Reason[];
      const userCount: Record<
        number,
        { name: string; email: string; problems: Set<string> }
      > = {};
      for (const reason of reasons) {
        const config = reason.configs.find(
          item => item.state === reason.state && !!item.ownerEmail?.length,
        );
        if (!!config) {
          userCount[config.ownerId] = userCount[config.ownerId] ?? {
            name: config.ownerName,
            email: config.ownerEmail,
            problems: new Set<string>(),
          };
          userCount[config.ownerId].problems.add(reason.problem.code);
        }
      }
      if (!!Object.keys(userCount).length) {
        this.mailService.sendTipEmails(Object.values(userCount), "audit");
      }
    }
    {
      const builder = this.reasonService.repository
        .createQueryBuilder("r")
        .innerJoinAndSelect("r.problem", "p")
        .innerJoinAndSelect("r.configs", "config")
        .where("r.delete is false")
        .andWhere("r.updatedAt < :deadline", {
          deadline: today.subtract(7, "day").toDate(),
        })
        .andWhere("r.state in (:...states)", {
          states: [NodeState.ANALYZE, NodeState.VALIDATE],
        })
        .andWhere(`p.status not in (:...statuses)`, {
          statuses: [
            ProblemStatus.DRAFT,
            ProblemStatus.CQE,
            ProblemStatus.OBSOLETE,
            ProblemStatus.CLOSED,
          ],
        });
      const reasons = (await this.reasonService.doQuery(
        builder,
        false,
      )) as Reason[];
      const userCount: Record<
        number,
        { name: string; email: string; problems: Set<string> }
      > = {};
      for (const reason of reasons) {
        const config = reason.configs.find(
          item => item.state === reason.state && !!item.ownerEmail?.length,
        );
        if (!!config) {
          userCount[config.ownerId] = userCount[config.ownerId] ?? {
            name: config.ownerName,
            email: config.ownerEmail,
            problems: new Set<string>(),
          };
          userCount[config.ownerId].problems.add(reason.problem.code);
        }
      }
      if (!!Object.keys(userCount).length) {
        this.mailService.sendTipEmails(Object.values(userCount), "task");
      }
    }
  }

  @Get("download/tpl")
  async getProblemTemplate(@Res() res: Response) {
    const exporter = new Exporter();
    const header = [
      { header: "创建日期", key: "createdOn" },
      { header: "事业部", key: "businessUnit" },
      { header: "客户", key: "customer" },
      { header: "项目编号", key: "projectCode" },
      { header: "产品阶段", key: "productStep" },
      { header: "问题描述", key: "description" },
      { header: "行动计划", key: "reason.improvement" },
      { header: "责任人", key: "reason.analyzeOwner" },
      { header: "预计完成日期", key: "reason.estimatedFinishOn" },
      { header: "原因类别(一级)", key: "reason.category" },
      { header: "原因类别(二级)", key: "reason.subCategory" },
    ];
    const sheet1 = exporter.addSheet({
      name: "问题列表",
      header,
    });
    const column1 = sheet1.getColumn("A");
    column1.numFmt = "yyyy/m/d";
    column1.eachCell(cell => {
      cell.numFmt = "@";
    });
    const column2 = sheet1.getColumn("I");
    column2.numFmt = "yyyy/m/d";
    column2.eachCell(cell => {
      cell.numFmt = "@";
    });
    const customers = await this.dictionaryService.getCategoryOptions(
      DictionaryCategory.CUSTOMER,
    );
    const businessUnits = await this.dictionaryService.getCategoryOptions(
      DictionaryCategory.BUSINESS_UNIT,
    );
    const productSteps = await this.dictionaryService.getCategoryOptions(
      DictionaryCategory.PRODUCT_STEP,
    );
    const { dataValidations } = sheet1 as any;
    (dataValidations as any).add("B2:B5000", {
      type: "list",
      allowBlank: false,
      formulae: [`事业部字典!$A$2:$A$${businessUnits.length}`],
    });
    exporter.addSheet({
      name: "事业部字典",
      header: [{ header: "选项", key: "name" }],
      data: businessUnits,
    });
    (dataValidations as any).add("C2:C5000", {
      type: "list",
      allowBlank: false,
      formulae: [`客户字典!$A$2:$A$${customers.length}`],
    });
    exporter.addSheet({
      name: "客户字典",
      header: [{ header: "选项", key: "name" }],
      data: customers,
    });
    (dataValidations as any).add("E2:E5000", {
      type: "list",
      allowBlank: false,
      formulae: [`产品阶段字典!$A$2:$A$${productSteps.length}`],
    });
    exporter.addSheet({
      name: "产品阶段字典",
      header: [{ header: "选项", key: "name" }],
      data: productSteps,
    });
    exporter.doExport({
      filename: "问题导入模板.xlsx",
      response: res,
    });
  }

  @Post("data/import")
  @UseInterceptors(
    FileInterceptor("file", {
      storage: memoryStorage(),
    }),
  )
  async importData(
    @Req() req: { payload: Payload },
    @Res() res: Response,
    @UploadedFile() file: Express.Multer.File,
  ) {
    const populator = new ExcelEntityPopulator(file.buffer);
    await populator.init();
    const header = [
      { header: "创建日期", key: "createdOn" },
      { header: "事业部", key: "businessUnit" },
      { header: "客户", key: "customer" },
      { header: "项目编号", key: "projectCode" },
      { header: "产品阶段", key: "productStep" },
      { header: "问题描述", key: "description" },
      { header: "行动计划", key: "reasonImprovement" },
      { header: "责任人", key: "reasonAnalyzeOwner" },
      { header: "预计完成日期", key: "reasonEstimatedFinishOn" },
      { header: "原因类别(一级)", key: "reasonCategory" },
      { header: "原因类别(二级)", key: "reasonSubCategory" },
    ];
    const psDict = await this.dictionaryService.getDictionaryByCategory(
      DictionaryCategory.PRODUCT_STEP,
    );
    const ctDict = await this.dictionaryService.getDictionaryByCategory(
      DictionaryCategory.CUSTOMER,
    );
    const buDict = await this.dictionaryService.getDictionaryByCategory(
      DictionaryCategory.BUSINESS_UNIT,
    );
    const user = await this.userService.findByToken(req.payload.token);
    const psOptions = psDict.options.map(item => item.name);
    const ctOptions = ctDict.options.map(item => item.name);
    const buOptions = buDict.options.map(item => item.name);
    const rows = populator.doImport<{
      createdOn: Date;
      businessUnit: string;
      customer: string;
      projectCode: string;
      productStep: string;
      description: string;
      reasonImprovement: string;
      reasonAnalyzeOwner: string;
      reasonEstimatedFinishOn: Date;
      reasonCategory: string;
      reasonSubCategory: string;
    }>(
      0,
      header.reduce((prev, curr) => {
        prev[curr.header] = curr.key;
        return prev;
      }, {}),
      [
        (row, cell, key) => {
          if (key === "productStep") {
            if (cell.value === null || !cell.value.toString().trim().length) {
              return "产品阶段不能为空";
            }
            if (!psOptions.includes(cell.value.toString().trim())) {
              return `产品阶段[${cell.value
                .toString()
                .trim()}]不在产品阶段字典中`;
            }
          } else if (key === "customer") {
            if (cell.value === null || !cell.value.toString().trim().length) {
              return "客户不能为空";
            }
            if (!ctOptions.includes(cell.value.toString().trim())) {
              return `客户[${cell.value.toString().trim()}]不在客户字典中`;
            }
          } else if (key === "businessUnit") {
            if (cell.value === null || !cell.value.toString().trim().length) {
              return undefined;
            }
            if (!buOptions.includes(cell.value.toString().trim())) {
              return `事业部[${cell.value.toString().trim()}]不在事业部字典中`;
            }
          } else if (key === "createdOn") {
            if (cell.value === null || !cell.value.toString().trim().length) {
              return "创建日期不可为空";
            }
            if (!(cell.value instanceof Date)) {
              return "创建日期必须是日期(例如2024/5/21)";
            }
          } else if (key === "reasonCategory") {
            if (cell.value === null || !cell.value.toString().trim().length) {
              return "原因类别(一级)不能为空";
            }
          } else if (key === "reasonSubCategory") {
            if (cell.value === null || !cell.value.toString().trim().length) {
              return "原因类别(二级)不能为空";
            }
          }
          return undefined;
        },
      ],
    );
    if (populator.hasError) {
      populator.outputErrors(file.originalname, res);
    } else {
      const problems: Problem[] = [];
      const reasons: Reason[] = [];
      const reasonConfigs: ReasonConfig[] = [];
      for (const row of rows) {
        const problem = new Problem();
        problem.createdOn = row.createdOn;
        problem.businessUnit = row.businessUnit;
        problem.customer = row.customer;
        problem.projectCode = row.projectCode;
        problem.productStep = row.productStep;
        problem.descriptions = { what: row.description } as ProblemDescription;
        problem.creatorId = user.id;
        problem.creatorName = user.name;
        problem.status = ProblemStatus.CLOSED;
        problems.push(problem);
        const reason = new Reason();
        reason.category = row.reasonCategory;
        reason.subCategory = row.reasonSubCategory;
        if (row.reasonEstimatedFinishOn instanceof Date) {
          reason.estimatedFinishOn = row.reasonEstimatedFinishOn;
        }
        reason.improvement = row.reasonImprovement;
        reason.status = ReasonStatus.CLOSED;
        reason.state = NodeState.COMPLETE;
        reason.stateIdx = 4;
        reason.problem = problem;
        reasons.push(reason);
        const reasonConfig = new ReasonConfig();
        reasonConfig.ownerId = -1;
        reasonConfig.ownerName = row.reasonAnalyzeOwner;
        reasonConfig.state = NodeState.ANALYZE;
        reasonConfig.stateIdx = 0;
        reasonConfig.reason = reason;
        reasonConfigs.push(reasonConfig);
      }
      if (!problems.length) {
        res
          .json({ code: 1, message: "没有可以导入的数据" })
          .status(HttpStatus.OK);
      } else {
        await this.service.execute(async manager => {
          for (const problem of problems) {
            await this.codeRuleService.genCode(
              problem,
              this.service.repository,
            );
            await manager.save(this.service.target, problem);
          }
          await manager
            .createQueryBuilder()
            .insert()
            .into(this.reasonService.target)
            .values(reasons)
            .execute();
          await manager
            .createQueryBuilder()
            .insert()
            .into(this.reasonConfigService.target)
            .values(reasonConfigs)
            .execute();
          return new Promise(resolve => {
            resolve(true);
          });
        });
        res.json({ code: 0 }).status(HttpStatus.OK);
      }
    }
  }
}
