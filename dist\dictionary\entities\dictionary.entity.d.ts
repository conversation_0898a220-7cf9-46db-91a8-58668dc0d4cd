import { NamedEntity, NumberIdObject } from "../../framework/model/base";
import { DictionaryOption } from "../types";
export declare enum DictionaryCategory {
    CUSTOMER = "CUSTOMER",
    BUSINESS_UNIT = "BUSINESS_UNIT",
    FACTORY = "FACTORY",
    PRODUCT_LINE = "PRODUCT_LINE",
    MACHINE_TYPE = "MACHINE_TYPE",
    PRODUCT_STEP = "PRODUCT_STEP",
    UNQUALITY_TYPE = "UNQUALITY_TYPE",
    UNQUALITY_CODE = "UNQUALITY_CODE",
    REASON_TYPE = "REASON_TYPE"
}
export declare const DictionaryCategoryOpions: Record<DictionaryCategory, string>;
export declare class Dictionary extends NumberIdObject implements NamedEntity<number> {
    name: string;
    description?: string;
    category: DictionaryCategory;
    options: DictionaryOption[];
    priority: number;
    tree: boolean;
}
export declare class DictionaryRelation extends NumberIdObject {
    machineType: string;
    customer: string;
    businessUnit: string;
}
