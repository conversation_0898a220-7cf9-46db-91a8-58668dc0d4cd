"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProblemModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const dictionary_module_1 = require("../dictionary/dictionary.module");
const code_rule_module_1 = require("./../code-rule/code-rule.module");
const mail_module_1 = require("./../mail/mail.module");
const audit_controller_1 = require("./audit.controller");
const problem_operate_log_entity_1 = require("./entities/problem-operate-log.entity");
const problem_entity_1 = require("./entities/problem.entity");
const reason_config_entity_1 = require("./entities/reason-config.entity");
const reason_detail_entity_1 = require("./entities/reason-detail.entity");
const reason_entity_1 = require("./entities/reason.entity");
const problem_operate_log_service_1 = require("./problem-operate-log.service");
const problem_controller_1 = require("./problem.controller");
const problem_service_1 = require("./problem.service");
const reason_detail_service_1 = require("./reason-detail.service");
const reason_service_1 = require("./reason.service");
const task_controller_1 = require("./task.controller");
const user_module_1 = require("../user/user.module");
const reason_config_service_1 = require("./reason-config.service");
let ProblemModule = class ProblemModule {
};
exports.ProblemModule = ProblemModule;
exports.ProblemModule = ProblemModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                problem_entity_1.Problem,
                problem_operate_log_entity_1.ProblemOperateLog,
                reason_entity_1.Reason,
                reason_detail_entity_1.ReasonDetail,
                reason_config_entity_1.ReasonConfig,
            ]),
            dictionary_module_1.DictionaryModule,
            code_rule_module_1.CodeRuleModule,
            mail_module_1.MailModule,
            user_module_1.UserModule,
        ],
        controllers: [problem_controller_1.ProblemController, task_controller_1.TaskController, audit_controller_1.AuditController],
        providers: [
            problem_service_1.ProblemService,
            problem_operate_log_service_1.ProblemOperateLogService,
            reason_config_service_1.ReasonConfigService,
            reason_service_1.ReasonService,
            reason_detail_service_1.ReasonDetailService,
        ],
        exports: [problem_service_1.ProblemService, reason_service_1.ReasonService],
    })
], ProblemModule);
//# sourceMappingURL=problem.module.js.map