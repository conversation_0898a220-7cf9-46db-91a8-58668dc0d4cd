export type IDType = string | number;
export interface IdEntity<ID extends IDType> {
    id: ID;
}
export interface NamedEntity<ID extends IDType> extends IdEntity<ID> {
    name: string;
}
export interface CodedEntity<ID extends IDType> extends IdEntity<ID> {
    code: string;
}
export declare abstract class NumberIdObject implements IdEntity<number> {
    id: number;
}
export declare abstract class NumberIdTimeObject extends NumberIdObject {
    createdAt: Date;
    updatedAt: Date;
}
export type Result<D = any> = {
    code: string;
    message: string;
    data: D;
};
export type Attachment = {
    bucket: string;
    extension: string;
    filename: string;
    key: string;
    size: number;
    type: string;
};
export type ProblemDescription = {
    what: string;
    why: string;
    where: string;
    when: string;
    who: string;
    how_detected: string;
    how_many: string;
};
