"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeRule = void 0;
const base_1 = require("../../framework/model/base");
const typeorm_1 = require("typeorm");
const dictionary_entity_1 = require("../../dictionary/entities/dictionary.entity");
let CodeRule = class CodeRule extends base_1.NumberIdObject {
    dictionary;
    option;
    code;
    description;
};
exports.CodeRule = CodeRule;
__decorate([
    (0, typeorm_1.ManyToOne)(() => dictionary_entity_1.Dictionary, { nullable: false, eager: true }),
    __metadata("design:type", dictionary_entity_1.Dictionary)
], CodeRule.prototype, "dictionary", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CodeRule.prototype, "option", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CodeRule.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 500 }),
    __metadata("design:type", String)
], CodeRule.prototype, "description", void 0);
exports.CodeRule = CodeRule = __decorate([
    (0, typeorm_1.Entity)()
], CodeRule);
//# sourceMappingURL=code-rule.entity.js.map