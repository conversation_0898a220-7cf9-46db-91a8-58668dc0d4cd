{"name": "strtok3", "version": "7.1.1", "description": "A promise based streaming tokenizer", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Borewit"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}, "scripts": {"clean": "del-cli lib/**/*.js lib/**/*.js.map lib/**/*.d.ts test/**/*.js test/**/*.js.map", "compile-src": "tsc -p lib", "compile-test": "tsc -p test", "compile": "npm run compile-src && npm run compile-test", "build": "npm run clean && npm run compile", "eslint": "eslint lib test", "lint-md": "remark -u preset-lint-recommended .", "lint": "npm run lint-md && npm run eslint", "fix": "eslint lib test --ext .ts --ignore-pattern *.d.ts --fix", "test": "mocha", "test-coverage": "c8 npm run test", "send-codacy": "c8 report --reporter=text-lcov | codacy-coverage", "start": "npm run compile && npm run lint && npm run cover-test"}, "engines": {"node": ">=16"}, "repository": {"type": "git", "url": "https://github.com/Borewit/strtok3.git"}, "license": "MIT", "type": "module", "exports": {".": {"node": "./lib/index.js", "default": "./lib/core.js"}, "./core": "./lib/core.js"}, "types": "lib/index.d.ts", "files": ["lib/**/*.js", "lib/**/*.d.ts"], "bugs": {"url": "https://github.com/Borewit/strtok3/issues"}, "devDependencies": {"@eslint/compat": "^1.1.1", "@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.7.0", "@types/chai": "^4.3.16", "@types/debug": "^4.1.12", "@types/mocha": "^10.0.7", "@types/node": "^20.14.10", "@typescript-eslint/eslint-plugin": "^7.16.1", "@typescript-eslint/parser": "^7.16.1", "c8": "^10.1.2", "chai": "^5.1.1", "del-cli": "^5.1.0", "eslint": "^9.7.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.7.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-unicorn": "^54.0.0", "global": "^4.4.0", "mocha": "^10.6.0", "remark-cli": "^12.0.1", "remark-preset-lint-recommended": "^7.0.0", "token-types": "^6.0.0", "ts-node": "^10.9.2", "typescript": "^5.5.3", "uint8array-extras": "^1.3.0"}, "dependencies": {"@tokenizer/token": "^0.3.0", "peek-readable": "^5.1.3"}, "keywords": ["tokenizer", "reader", "token", "async", "promise", "parser", "decoder", "binary", "endian", "uint", "stream", "streaming"], "packageManager": "yarn@4.3.1"}