import { QueryRequest } from "src/framework/service/Query";
import { DataSource, Repository } from "typeorm";
import { BaseService } from "../framework/service/base.service";
import { DictionaryRelation } from "./entities/dictionary.entity";
export declare class DictionaryRelationService extends BaseService<DictionaryRelation> {
    constructor(repository: Repository<DictionaryRelation>, dataSource: DataSource);
    processUpdate(source: Partial<DictionaryRelation>, target: DictionaryRelation): void;
    query(request: QueryRequest): Promise<import("../framework/service/IBase.service").Page<DictionaryRelation> | DictionaryRelation[]>;
}
