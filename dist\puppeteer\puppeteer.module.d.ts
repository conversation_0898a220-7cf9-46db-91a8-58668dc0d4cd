import { DynamicModule } from "@nestjs/common";
import { PuppeteerModuleAsyncOptions, PuppeteerModuleOptions } from "./interfaces";
export declare class PuppeteerModule {
    static forRoot(options?: PuppeteerModuleOptions): DynamicModule;
    static forFeature(pages: string[], browser?: PuppeteerModuleOptions | string): DynamicModule;
    static forRootAsync(options: PuppeteerModuleAsyncOptions): DynamicModule;
}
