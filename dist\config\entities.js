"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const code_rule_entity_1 = require("../code-rule/entities/code-rule.entity");
const dictionary_entity_1 = require("../dictionary/entities/dictionary.entity");
const problem_operate_log_entity_1 = require("../problem/entities/problem-operate-log.entity");
const problem_entity_1 = require("../problem/entities/problem.entity");
const reason_config_entity_1 = require("../problem/entities/reason-config.entity");
const reason_detail_entity_1 = require("../problem/entities/reason-detail.entity");
const reason_entity_1 = require("../problem/entities/reason.entity");
exports.default = [
    code_rule_entity_1.CodeRule,
    dictionary_entity_1.Dictionary,
    problem_operate_log_entity_1.ProblemOperateLog,
    problem_entity_1.Problem,
    reason_config_entity_1.ReasonConfig,
    reason_detail_entity_1.ReasonDetail,
    reason_entity_1.Reason,
];
//# sourceMappingURL=entities.js.map