"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProblemController = exports.reasonStorage = exports.ReasonStateOptions = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const schedule_1 = require("@nestjs/schedule");
const dayjs_1 = __importDefault(require("dayjs"));
const fs_1 = require("fs");
const multer_1 = require("multer");
const path_1 = require("path");
const pdfkit_1 = __importDefault(require("pdfkit"));
const puppeteer_core_1 = require("puppeteer-core");
const public_decorator_1 = require("../auth/public.decorator");
const dictionary_service_1 = require("../dictionary/dictionary.service");
const dictionary_entity_1 = require("../dictionary/entities/dictionary.entity");
const mail_service_1 = require("../mail/mail.service");
const user_service_1 = require("../user/user.service");
const exportData_1 = __importDefault(require("../utils/excel/exportData"));
const importData_1 = require("../utils/excel/importData");
const file_upload_utils_1 = require("../utils/file-upload.utils");
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const code_rule_service_1 = require("./../code-rule/code-rule.service");
const properties_1 = require("./../utils/properties");
const create_reason_dto_1 = require("./dto/create-reason.dto");
const constant_1 = require("./entities/constant");
const problem_entity_1 = require("./entities/problem.entity");
const reason_config_entity_1 = require("./entities/reason-config.entity");
const reason_detail_entity_1 = require("./entities/reason-detail.entity");
const reason_entity_1 = require("./entities/reason.entity");
const problem_operate_log_service_1 = require("./problem-operate-log.service");
const problem_service_1 = require("./problem.service");
const reason_config_service_1 = require("./reason-config.service");
const reason_detail_service_1 = require("./reason-detail.service");
const reason_service_1 = require("./reason.service");
const ProblemStatusOptions = {
    [constant_1.ProblemStatus.DRAFT]: "草稿",
    [constant_1.ProblemStatus.CQE]: "待CQE分配",
    [constant_1.ProblemStatus.NEW]: "待处理",
    [constant_1.ProblemStatus.PROCESSING]: "处理中",
    [constant_1.ProblemStatus.CLOSED]: "已关闭",
    [constant_1.ProblemStatus.OBSOLETE]: "已作废",
};
exports.ReasonStateOptions = {
    [constant_1.NodeState.ANALYZE]: "原因分析",
    [constant_1.NodeState.ANALYZE_AUDIT]: "原因分析审核",
    [constant_1.NodeState.VALIDATE]: "效果验证",
    [constant_1.NodeState.VALIDATE_AUDIT]: "效果验证审核",
    [constant_1.NodeState.CQE_AUDIT]: "CQE经理审核",
    [constant_1.NodeState.COMPLETE]: "完成",
};
const problemStorage = (0, multer_1.diskStorage)({
    destination: (0, path_1.join)(properties_1.config.baseStoreageDir, "problem"),
    filename: (req, file, cb) => {
        const extension = (0, path_1.extname)(file.originalname);
        const randomName = (0, uuid_1.v4)();
        cb(null, `${randomName}${extension}`);
    },
});
exports.reasonStorage = (0, multer_1.diskStorage)({
    destination: (0, path_1.join)(properties_1.config.baseStoreageDir, "reason"),
    filename: (req, file, cb) => {
        const extension = (0, path_1.extname)(file.originalname);
        const randomName = (0, uuid_1.v4)();
        cb(null, `${randomName}${extension}`);
    },
});
let ProblemController = class ProblemController {
    service;
    logService;
    reasonService;
    reasonConfigService;
    reasonDetailService;
    mailService;
    codeRuleService;
    dictionaryService;
    userService;
    browser;
    constructor(service, logService, reasonService, reasonConfigService, reasonDetailService, mailService, codeRuleService, dictionaryService, userService, browser) {
        this.service = service;
        this.logService = logService;
        this.reasonService = reasonService;
        this.reasonConfigService = reasonConfigService;
        this.reasonDetailService = reasonDetailService;
        this.mailService = mailService;
        this.codeRuleService = codeRuleService;
        this.dictionaryService = dictionaryService;
        this.userService = userService;
        this.browser = browser;
    }
    async convertImageToPdf(imageBuffer, dimensions) {
        return new Promise(resolve => {
            const doc = new pdfkit_1.default({ autoFirstPage: false });
            const chunks = [];
            doc.addPage({
                size: [dimensions.width + 30, dimensions.height + 30],
            });
            doc.image(imageBuffer, 15, 15, {
                fit: [dimensions.width, dimensions.height],
            });
            doc.on("data", chunk => {
                chunks.push(chunk);
            });
            doc.on("end", () => {
                const resultBuffer = Buffer.concat(chunks);
                resolve(resultBuffer);
            });
            doc.end();
        });
    }
    async pdf(req, id, res) {
        const user = await this.userService.findByToken(req.payload.token);
        const problem = await this.service.get(id);
        const page = await this.browser.newPage();
        await page.setViewport({ width: 1920, height: 9999 });
        const token = user.languageCode === "en" ? 2 : 1;
        const file = user.languageCode === "en" ? "Problem_" : "问题";
        try {
            await page.goto(`${properties_1.config.carUrl}?authKey=${token}&redirect=/pdf/${id}`, {
                waitUntil: "domcontentloaded",
            });
            await page.waitForNavigation({ timeout: 20000 });
            await page.waitForNetworkIdle({ timeout: 20000 });
            await page.content();
            const bodyHandlers = await page.$$("#app > .v-descriptions");
            let _height = 0;
            for (const item of bodyHandlers) {
                const { height } = await item.boundingBox();
                _height += height;
            }
            await page.setViewport({
                width: Math.ceil(1920),
                height: Math.ceil(_height),
                deviceScaleFactor: 2,
            });
            const buffer = await page.screenshot({
                fullPage: true,
            });
            const pdfBuffer = await this.convertImageToPdf(buffer, {
                width: 1920,
                height: _height,
            });
            res.contentType("application/pdf");
            res.setHeader("Content-Disposition", `attachment; filename*=${encodeURIComponent(file + "[" + problem.code + "].pdf")}`);
            res.send(pdfBuffer);
        }
        finally {
            await page.close();
        }
    }
    async query(req, request) {
        return await this.service.query(request);
    }
    async export(req, request, res) {
        const langs = await this.userService.getLanguages();
        const user = await this.userService.findByToken(req.payload.token);
        const lang = langs.find(item => item.code === (user.languageCode ?? "zh"));
        const trans = lang.langData.reduce((prev, curr) => {
            prev[curr.label] = curr.value;
            return prev;
        }, {});
        const t = (key) => {
            return trans[key] ?? key;
        };
        let problems;
        if (Array.isArray(request.ids) && !!request.ids?.length) {
            problems = (await this.service.query({
                params: { id: request.ids },
                operations: { id: "IN" },
            }));
        }
        else {
            problems = (await this.service.query(request));
        }
        const file = user.languageCode === "en" ? "Problem" : "问题";
        const exporter = new exportData_1.default();
        const sheet = exporter.addSheet({
            header: [
                { header: t("问题编号"), key: "code" },
                { header: t("创建日期"), key: "createdOn" },
                { header: t("机型"), key: "machineType" },
                { header: t("客户"), key: "customer" },
                { header: t("项目编号"), key: "projectCode" },
                { header: t("工单号"), key: "workOrderCode" },
                { header: t("工单数量"), key: "workOrderNum" },
                { header: t("和而泰工厂"), key: "factory" },
                { header: t("事业部"), key: "businessUnit" },
                { header: t("创建人"), key: "creatorName" },
                { header: t("产品阶段"), key: "productStep" },
                { header: t("问题描述"), key: "descriptions" },
                { header: t("状态"), key: "status" },
                { header: t("当前节点"), key: "node" },
                { header: t("责任人"), key: "responsor" },
                { header: t("下一节点"), key: "nextNode" },
            ],
            data: problems.map(({ code, createdOn, machineType, customer, projectCode, workOrderCode, workOrderNum, factory, businessUnit, creatorName, productStep, descriptions, status, reasons, }) => ({
                code,
                createdOn,
                machineType: t(machineType),
                customer: t(customer),
                projectCode,
                workOrderCode,
                workOrderNum,
                factory: t(factory),
                businessUnit: t(businessUnit),
                creatorName,
                productStep,
                descriptions: user.languageCode === "en"
                    ? `1.What happened:${descriptions?.what ?? ""}\n2.Why is it an issue:${descriptions?.why ?? ""}\n3.Where was the issue detected:${descriptions?.where ?? ""}\n4.When detected:${descriptions?.when ?? ""};\n5.Who detected the issue:${descriptions?.who ?? ""}\n6.How was the issue detected:${descriptions?.how_detected ?? ""}\n7.How many:${descriptions?.how_many ?? ""}`
                    : `1.发生了什么:${descriptions?.what ?? ""}\n2.为什么是一个问题:${descriptions?.why ?? ""}\n3.在哪里发现这个问题:${descriptions?.where ?? ""}\n4.什么时候发现:${descriptions?.when ?? ""};\n5.谁发现这个问题:${descriptions?.who ?? ""}\n6.怎么发现的这个问题:${descriptions?.how_detected ?? ""}\n7.发现数量:${descriptions?.how_many ?? ""}`,
                status: t(ProblemStatusOptions[status]),
                node: reasons
                    .map(reason => t(exports.ReasonStateOptions[reason.state]))
                    .join("\n"),
                responsor: reasons
                    .map(reason => reason.configs.find(config => config.state === reason.state)
                    ?.ownerName ?? "-")
                    .join("\n"),
                nextNode: reasons
                    .map(reason => {
                    const current = reason.configs.find(config => config.state === reason.state);
                    const next = reason.configs.find(config => config.stateIdx === (current?.stateIdx ?? -999) + 1);
                    return `${!!next && !!next.state ? t(exports.ReasonStateOptions[next.state]) : "-"}${!!next && !!next.ownerName?.length
                        ? "[" + next.ownerName + "]"
                        : ""}`;
                })
                    .join("\n"),
            })),
        });
        sheet.columns.forEach(column => {
            if (column.key === "descriptions" ||
                column.key === "node" ||
                column.key === "responsor" ||
                column.key === "nextNode") {
                column.eachCell((cell, idx) => {
                    if (idx > 1) {
                        cell.style = {
                            ...cell.style,
                            alignment: {
                                ...cell.style?.alignment,
                                horizontal: column.key === "descriptions" ? "left" : "center",
                                wrapText: true,
                            },
                        };
                    }
                });
            }
        });
        exporter.doExport({
            filename: file + "_" + (0, dayjs_1.default)().format("YYYYMMDDHHmmss") + ".xlsx",
            response: res,
        });
    }
    async info(id) {
        return await this.service.get(id);
    }
    async save(request, entity) {
        if (!entity.id) {
            entity.creatorId = request.payload.id;
            entity.creatorName = request.payload.name;
        }
        else {
            const problem = await this.service.get(entity.id);
            if (problem.creatorId !== request.payload.id &&
                problem.cqeId !== request.payload.id) {
                throw new common_1.ForbiddenException("没有权限修改问题");
            }
        }
        return await this.service.saveOrUpdate(entity);
    }
    async submit(request, id) {
        const problem = await this.service.get(id);
        if (problem.status === constant_1.ProblemStatus.DRAFT) {
            if (problem.creatorId !== request.payload.id) {
                throw new common_1.ForbiddenException("只可以提交自己创建的问题");
            }
            problem.status = constant_1.ProblemStatus.CQE;
            const result = await this.service.execute(async (manager) => {
                await this.codeRuleService.genCode(problem, this.service.repository);
                try {
                    await this.userService.addTodo(request.payload.token, [], problem, constant_1.NodeState.ANALYZE);
                }
                catch { }
                const log = this.logService.repository.create({
                    problem: { id },
                    operatorId: request.payload.id,
                    operatorName: request.payload.name,
                    action: {
                        key: "problem.action.submit_cqe",
                        message: "创建问题并提交CQE分配",
                    },
                });
                await manager.save(this.logService.target, log);
                return await manager.save(this.service.target, problem);
            });
            return result;
        }
        else if (problem.status === constant_1.ProblemStatus.CQE) {
            if (problem.cqeId !== request.payload.id) {
                throw new common_1.ForbiddenException("不是指定的CQE,无法提交");
            }
            problem.status = constant_1.ProblemStatus.NEW;
            const result = await this.service.execute(async (manager) => {
                const log = this.logService.repository.create({
                    problem: { id },
                    operatorId: request.payload.id,
                    operatorName: request.payload.name,
                    action: { key: "problem.action.submit", message: "提交了问题" },
                });
                await manager.save(this.logService.target, log);
                return await manager.save(this.service.target, problem);
            });
            const reasons = await this.reasonService.findByProblemId(result.id);
            try {
                if (!!result.todoId) {
                    await this.userService.processedTodo(request.payload.token, [
                        result.todoId,
                    ]);
                }
            }
            catch { }
            const results = await this.userService.addTodo(request.payload.token, reasons, problem, constant_1.NodeState.ANALYZE);
            if (results?.length) {
                await this.reasonService.execute(async (manager) => {
                    return manager.save(this.reasonService.target, results);
                });
            }
            this.mailService.sendEmail(reasons, constant_1.NodeState.ANALYZE, problem);
            return result;
        }
        else {
            throw new common_1.ForbiddenException("当前状态不可提交");
        }
    }
    async remove(req, request) {
        const user = await this.userService.findByToken(req.payload.token);
        const isAdmin = (user.roleId ?? []).includes("1");
        const conditions = { id: (0, typeorm_1.In)(request.ids) };
        if (!isAdmin) {
            conditions.creatorId = (0, typeorm_1.Equal)(req.payload.id);
        }
        const entities = await this.service.repository.findBy(conditions);
        await this.service.deleteEntities(entities, isAdmin, async (toObsolete) => {
            const todoIds = new Set();
            for (const p of toObsolete) {
                const reasons = await this.reasonService.findByProblemId(p.id);
                reasons
                    .filter(reason => !!reason.todoId)
                    .forEach(reason => todoIds.add(reason.todoId));
            }
            if (todoIds.size > 0) {
                await this.userService.processedTodo(req.payload.token, Array.from(todoIds), "2");
            }
        });
    }
    async uploadFile(id, type, file) {
        const problem = await this.service.get(id);
        if (!!file) {
            let attachments;
            if (type === "good") {
                problem.goodPartImages = problem.goodPartImages ?? [];
                attachments = problem.goodPartImages;
            }
            else {
                problem.badPartImages = problem.badPartImages ?? [];
                attachments = problem.badPartImages;
            }
            attachments.push({
                bucket: file.destination,
                extension: (0, path_1.extname)(file.originalname),
                filename: file.originalname,
                key: (0, path_1.parse)(file.filename).name,
                size: file.size,
                type: file.mimetype,
            });
            return await this.service.execute(manager => manager.save(problem_entity_1.Problem, problem));
        }
        throw new common_1.InternalServerErrorException("File upload failed");
    }
    async removeFile(type, id, key) {
        const entity = await this.service.get(id);
        const attachments = (type === "good" ? entity.goodPartImages : entity.badPartImages) ?? [];
        const idx = attachments.findIndex(item => item.key === key);
        if (idx > -1) {
            const [attachment] = attachments.splice(idx, 1);
            if (!!attachment) {
                try {
                    (0, fs_1.unlinkSync)((0, path_1.join)(attachment.bucket, `${attachment.key}${attachment.extension}`));
                }
                catch { }
            }
        }
        return await this.service.execute(manager => manager.save(this.service.target, entity));
    }
    async attachment(type, id, key, res) {
        const problem = await this.service.get(id);
        const attachments = (type === "good" ? problem.goodPartImages : problem.badPartImages) ?? [];
        const attachment = attachments.find(item => item.key === key);
        if (!attachment) {
            throw new common_1.InternalServerErrorException("没有找到附件");
        }
        res.sendFile((0, path_1.join)(attachment.bucket, `${attachment.key}${attachment.extension}`), {
            headers: {
                "Content-Disposition": `attachment; filename*=${encodeURIComponent(attachment.filename)}`,
            },
        });
    }
    async logs(id) {
        return this.logService.findByProblemId(id);
    }
    async reasons(id) {
        return this.reasonService.findByProblemId(id);
    }
    async addOrModifyReason(request, id, entity) {
        const problem = await this.service.get(id);
        if (problem.status === constant_1.ProblemStatus.CLOSED ||
            problem.status === constant_1.ProblemStatus.OBSOLETE) {
            throw new common_1.ForbiddenException("问题已关闭,非法操作");
        }
        const isNew = !entity.id && problem.status !== constant_1.ProblemStatus.CQE;
        const target = !!entity.id
            ? await this.reasonService.get(entity.id)
            : this.reasonService.repository.create({
                ...entity,
                problem: { id },
            });
        const reason = await this.reasonService.execute(async (manager) => {
            target.category = entity.category;
            target.subCategory = entity.subCategory;
            target.configs = [
                ...entity.configs.map(config => ({
                    ...config,
                    stateIdx: constant_1.nodes.find(node => node.state === config.state)?.index ?? -1,
                })),
            ];
            return manager.save(this.reasonService.target, target);
        });
        if (isNew) {
            const results = await this.userService.addTodo(request.payload.token, [reason], problem, constant_1.NodeState.ANALYZE);
            if (results?.length) {
                await this.reasonService.execute(async (manager) => {
                    return manager.save(this.reasonService.target, results);
                });
            }
            this.mailService.sendEmail([reason], constant_1.NodeState.ANALYZE, problem);
        }
        return reason;
    }
    async deleteReason(req, id, request) {
        const problem = await this.service.get(id);
        const reasons = await this.reasonService.findByProblemId(id);
        if (problem.status === constant_1.ProblemStatus.DRAFT) {
            await this.reasonService.delete(reasons
                .filter(reason => request.ids.indexOf(reason.id) > -1)
                .map(reason => reason.id));
        }
        else if (problem.status === constant_1.ProblemStatus.CLOSED) {
            throw new common_1.ForbiddenException("问题已关闭,非法操作");
        }
        else {
            await this.reasonService.execute(async (manager) => {
                const toDelete = reasons.filter(reason => request.ids.indexOf(reason.id) > -1);
                const logs = [];
                toDelete.forEach(item => {
                    item.delete = true;
                    item.deleteById = req.payload.id;
                    item.deleteByName = req.payload.name;
                    const log = this.logService.repository.create({
                        problem: { id },
                        operatorId: req.payload.id,
                        operatorName: req.payload.name,
                        action: {
                            key: "problem.action.obsolete_reason",
                            message: "作废了原因",
                        },
                    });
                    logs.push(log);
                });
                const todoIds = toDelete
                    .filter(item => !!item.todoId)
                    .map(item => item.todoId);
                if (!!todoIds.length) {
                    await this.userService.processedTodo(req.payload.token, todoIds, "2");
                }
                manager.save(this.logService.target, logs);
                return manager.save(this.reasonService.target, toDelete);
            });
        }
    }
    async updateReason(id, entity) {
        const problem = await this.service.get(id);
        const target = await this.reasonService.get(entity.id);
        if (problem.status === constant_1.ProblemStatus.CLOSED) {
            throw new common_1.ForbiddenException("问题已关闭,非法操作");
        }
        else if (problem.status === constant_1.ProblemStatus.DRAFT) {
            return this.reasonService.execute(async (manager) => {
                target.configs = entity.configs;
                return manager.save(this.reasonService.target, target);
            });
        }
        else {
            return this.reasonService.saveOrUpdate(entity);
        }
    }
    async saveOrUpdateReasonDetail(id, entity, file) {
        const reason = await this.reasonService.get(id);
        if (reason.status === constant_1.ReasonStatus.CLOSED ||
            reason.status === constant_1.ReasonStatus.FOLLOW) {
            throw new common_1.ForbiddenException("流程正在进行中或已关闭,非法操作");
        }
        if (!!file) {
            entity.attachment = {
                bucket: file.destination,
                extension: (0, path_1.extname)(file.originalname),
                filename: file.originalname,
                key: (0, path_1.parse)(file.filename).name,
                size: file.size,
                type: file.mimetype,
            };
        }
        return this.reasonDetailService.saveOrUpdate(entity);
    }
    async deleteReasonDetail(id, request) {
        const reason = await this.reasonService.get(id);
        if (reason.status === constant_1.ReasonStatus.CLOSED ||
            reason.status === constant_1.ReasonStatus.FOLLOW) {
            throw new common_1.ForbiddenException("流程正在进行中或已关闭,非法操作");
        }
        const details = await this.reasonDetailService.findByReasonId(id);
        await this.reasonDetailService.delete(details
            .filter(entity => request.ids.indexOf(entity.id) > -1)
            .map(entity => entity.id));
    }
    async sendMails(req) {
        const user = await this.userService.findByToken(req.payload.token);
        const isAdmin = (user.roleId ?? []).includes("1");
        if (isAdmin) {
            const builder = this.reasonService.repository
                .createQueryBuilder("r")
                .innerJoinAndSelect("r.problem", "p")
                .innerJoinAndSelect("r.configs", "config")
                .where("r.delete is false")
                .andWhere("r.state in (:...states)", {
                states: [
                    constant_1.NodeState.ANALYZE,
                    constant_1.NodeState.ANALYZE_AUDIT,
                    constant_1.NodeState.VALIDATE,
                    constant_1.NodeState.VALIDATE_AUDIT,
                ],
            })
                .andWhere(`p.status not in (:...statuses)`, {
                statuses: [
                    constant_1.ProblemStatus.DRAFT,
                    constant_1.ProblemStatus.OBSOLETE,
                    constant_1.ProblemStatus.CLOSED,
                ],
            });
            const reasons = (await this.reasonService.doQuery(builder, false));
            const problems = {};
            for (const reason of reasons) {
                problems[reason.problem.id] =
                    problems[reason.problem.id] ??
                        {};
                problems[reason.problem.id][reason.state] =
                    problems[reason.problem.id][reason.state] ?? [];
                problems[reason.problem.id][reason.state].push(reason);
            }
            for (const stateReason of Object.values(problems)) {
                for (const [state, reasons] of Object.entries(stateReason)) {
                    await this.mailService.sendEmail(reasons, state, reasons[0].problem);
                }
            }
        }
    }
    async handleCron() {
        const today = (0, dayjs_1.default)();
        {
            const builder = this.reasonService.repository
                .createQueryBuilder("r")
                .innerJoinAndSelect("r.problem", "p")
                .innerJoinAndSelect("r.configs", "config")
                .where("r.delete is false")
                .andWhere("r.updatedAt < :deadline", {
                deadline: today.subtract(7, "day").toDate(),
            })
                .andWhere("r.state in (:...states)", {
                states: [
                    constant_1.NodeState.ANALYZE_AUDIT,
                    constant_1.NodeState.VALIDATE_AUDIT,
                    constant_1.NodeState.CQE_AUDIT,
                ],
            })
                .andWhere(`p.status not in (:...statuses)`, {
                statuses: [
                    constant_1.ProblemStatus.DRAFT,
                    constant_1.ProblemStatus.CQE,
                    constant_1.ProblemStatus.OBSOLETE,
                    constant_1.ProblemStatus.CLOSED,
                ],
            });
            const reasons = (await this.reasonService.doQuery(builder, false));
            const userCount = {};
            for (const reason of reasons) {
                const config = reason.configs.find(item => item.state === reason.state && !!item.ownerEmail?.length);
                if (!!config) {
                    userCount[config.ownerId] = userCount[config.ownerId] ?? {
                        name: config.ownerName,
                        email: config.ownerEmail,
                        problems: new Set(),
                    };
                    userCount[config.ownerId].problems.add(reason.problem.code);
                }
            }
            if (!!Object.keys(userCount).length) {
                this.mailService.sendTipEmails(Object.values(userCount), "audit");
            }
        }
        {
            const builder = this.reasonService.repository
                .createQueryBuilder("r")
                .innerJoinAndSelect("r.problem", "p")
                .innerJoinAndSelect("r.configs", "config")
                .where("r.delete is false")
                .andWhere("r.updatedAt < :deadline", {
                deadline: today.subtract(7, "day").toDate(),
            })
                .andWhere("r.state in (:...states)", {
                states: [constant_1.NodeState.ANALYZE, constant_1.NodeState.VALIDATE],
            })
                .andWhere(`p.status not in (:...statuses)`, {
                statuses: [
                    constant_1.ProblemStatus.DRAFT,
                    constant_1.ProblemStatus.CQE,
                    constant_1.ProblemStatus.OBSOLETE,
                    constant_1.ProblemStatus.CLOSED,
                ],
            });
            const reasons = (await this.reasonService.doQuery(builder, false));
            const userCount = {};
            for (const reason of reasons) {
                const config = reason.configs.find(item => item.state === reason.state && !!item.ownerEmail?.length);
                if (!!config) {
                    userCount[config.ownerId] = userCount[config.ownerId] ?? {
                        name: config.ownerName,
                        email: config.ownerEmail,
                        problems: new Set(),
                    };
                    userCount[config.ownerId].problems.add(reason.problem.code);
                }
            }
            if (!!Object.keys(userCount).length) {
                this.mailService.sendTipEmails(Object.values(userCount), "task");
            }
        }
    }
    async getProblemTemplate(res) {
        const exporter = new exportData_1.default();
        const header = [
            { header: "创建日期", key: "createdOn" },
            { header: "事业部", key: "businessUnit" },
            { header: "客户", key: "customer" },
            { header: "项目编号", key: "projectCode" },
            { header: "产品阶段", key: "productStep" },
            { header: "问题描述", key: "description" },
            { header: "行动计划", key: "reason.improvement" },
            { header: "责任人", key: "reason.analyzeOwner" },
            { header: "预计完成日期", key: "reason.estimatedFinishOn" },
            { header: "原因类别(一级)", key: "reason.category" },
            { header: "原因类别(二级)", key: "reason.subCategory" },
        ];
        const sheet1 = exporter.addSheet({
            name: "问题列表",
            header,
        });
        const column1 = sheet1.getColumn("A");
        column1.numFmt = "yyyy/m/d";
        column1.eachCell(cell => {
            cell.numFmt = "@";
        });
        const column2 = sheet1.getColumn("I");
        column2.numFmt = "yyyy/m/d";
        column2.eachCell(cell => {
            cell.numFmt = "@";
        });
        const customers = await this.dictionaryService.getCategoryOptions(dictionary_entity_1.DictionaryCategory.CUSTOMER);
        const businessUnits = await this.dictionaryService.getCategoryOptions(dictionary_entity_1.DictionaryCategory.BUSINESS_UNIT);
        const productSteps = await this.dictionaryService.getCategoryOptions(dictionary_entity_1.DictionaryCategory.PRODUCT_STEP);
        const { dataValidations } = sheet1;
        dataValidations.add("B2:B5000", {
            type: "list",
            allowBlank: false,
            formulae: [`事业部字典!$A$2:$A$${businessUnits.length}`],
        });
        exporter.addSheet({
            name: "事业部字典",
            header: [{ header: "选项", key: "name" }],
            data: businessUnits,
        });
        dataValidations.add("C2:C5000", {
            type: "list",
            allowBlank: false,
            formulae: [`客户字典!$A$2:$A$${customers.length}`],
        });
        exporter.addSheet({
            name: "客户字典",
            header: [{ header: "选项", key: "name" }],
            data: customers,
        });
        dataValidations.add("E2:E5000", {
            type: "list",
            allowBlank: false,
            formulae: [`产品阶段字典!$A$2:$A$${productSteps.length}`],
        });
        exporter.addSheet({
            name: "产品阶段字典",
            header: [{ header: "选项", key: "name" }],
            data: productSteps,
        });
        exporter.doExport({
            filename: "问题导入模板.xlsx",
            response: res,
        });
    }
    async importData(req, res, file) {
        const populator = new importData_1.ExcelEntityPopulator(file.buffer);
        await populator.init();
        const header = [
            { header: "创建日期", key: "createdOn" },
            { header: "事业部", key: "businessUnit" },
            { header: "客户", key: "customer" },
            { header: "项目编号", key: "projectCode" },
            { header: "产品阶段", key: "productStep" },
            { header: "问题描述", key: "description" },
            { header: "行动计划", key: "reasonImprovement" },
            { header: "责任人", key: "reasonAnalyzeOwner" },
            { header: "预计完成日期", key: "reasonEstimatedFinishOn" },
            { header: "原因类别(一级)", key: "reasonCategory" },
            { header: "原因类别(二级)", key: "reasonSubCategory" },
        ];
        const psDict = await this.dictionaryService.getDictionaryByCategory(dictionary_entity_1.DictionaryCategory.PRODUCT_STEP);
        const ctDict = await this.dictionaryService.getDictionaryByCategory(dictionary_entity_1.DictionaryCategory.CUSTOMER);
        const buDict = await this.dictionaryService.getDictionaryByCategory(dictionary_entity_1.DictionaryCategory.BUSINESS_UNIT);
        const user = await this.userService.findByToken(req.payload.token);
        const psOptions = psDict.options.map(item => item.name);
        const ctOptions = ctDict.options.map(item => item.name);
        const buOptions = buDict.options.map(item => item.name);
        const rows = populator.doImport(0, header.reduce((prev, curr) => {
            prev[curr.header] = curr.key;
            return prev;
        }, {}), [
            (row, cell, key) => {
                if (key === "productStep") {
                    if (cell.value === null || !cell.value.toString().trim().length) {
                        return "产品阶段不能为空";
                    }
                    if (!psOptions.includes(cell.value.toString().trim())) {
                        return `产品阶段[${cell.value
                            .toString()
                            .trim()}]不在产品阶段字典中`;
                    }
                }
                else if (key === "customer") {
                    if (cell.value === null || !cell.value.toString().trim().length) {
                        return "客户不能为空";
                    }
                    if (!ctOptions.includes(cell.value.toString().trim())) {
                        return `客户[${cell.value.toString().trim()}]不在客户字典中`;
                    }
                }
                else if (key === "businessUnit") {
                    if (cell.value === null || !cell.value.toString().trim().length) {
                        return undefined;
                    }
                    if (!buOptions.includes(cell.value.toString().trim())) {
                        return `事业部[${cell.value.toString().trim()}]不在事业部字典中`;
                    }
                }
                else if (key === "createdOn") {
                    if (cell.value === null || !cell.value.toString().trim().length) {
                        return "创建日期不可为空";
                    }
                    if (!(cell.value instanceof Date)) {
                        return "创建日期必须是日期(例如2024/5/21)";
                    }
                }
                else if (key === "reasonCategory") {
                    if (cell.value === null || !cell.value.toString().trim().length) {
                        return "原因类别(一级)不能为空";
                    }
                }
                else if (key === "reasonSubCategory") {
                    if (cell.value === null || !cell.value.toString().trim().length) {
                        return "原因类别(二级)不能为空";
                    }
                }
                return undefined;
            },
        ]);
        if (populator.hasError) {
            populator.outputErrors(file.originalname, res);
        }
        else {
            const problems = [];
            const reasons = [];
            const reasonConfigs = [];
            for (const row of rows) {
                const problem = new problem_entity_1.Problem();
                problem.createdOn = row.createdOn;
                problem.businessUnit = row.businessUnit;
                problem.customer = row.customer;
                problem.projectCode = row.projectCode;
                problem.productStep = row.productStep;
                problem.descriptions = { what: row.description };
                problem.creatorId = user.id;
                problem.creatorName = user.name;
                problem.status = constant_1.ProblemStatus.CLOSED;
                problems.push(problem);
                const reason = new reason_entity_1.Reason();
                reason.category = row.reasonCategory;
                reason.subCategory = row.reasonSubCategory;
                if (row.reasonEstimatedFinishOn instanceof Date) {
                    reason.estimatedFinishOn = row.reasonEstimatedFinishOn;
                }
                reason.improvement = row.reasonImprovement;
                reason.status = constant_1.ReasonStatus.CLOSED;
                reason.state = constant_1.NodeState.COMPLETE;
                reason.stateIdx = 4;
                reason.problem = problem;
                reasons.push(reason);
                const reasonConfig = new reason_config_entity_1.ReasonConfig();
                reasonConfig.ownerId = -1;
                reasonConfig.ownerName = row.reasonAnalyzeOwner;
                reasonConfig.state = constant_1.NodeState.ANALYZE;
                reasonConfig.stateIdx = 0;
                reasonConfig.reason = reason;
                reasonConfigs.push(reasonConfig);
            }
            if (!problems.length) {
                res
                    .json({ code: 1, message: "没有可以导入的数据" })
                    .status(common_1.HttpStatus.OK);
            }
            else {
                await this.service.execute(async (manager) => {
                    for (const problem of problems) {
                        await this.codeRuleService.genCode(problem, this.service.repository);
                        await manager.save(this.service.target, problem);
                    }
                    await manager
                        .createQueryBuilder()
                        .insert()
                        .into(this.reasonService.target)
                        .values(reasons)
                        .execute();
                    await manager
                        .createQueryBuilder()
                        .insert()
                        .into(this.reasonConfigService.target)
                        .values(reasonConfigs)
                        .execute();
                    return new Promise(resolve => {
                        resolve(true);
                    });
                });
                res.json({ code: 0 }).status(common_1.HttpStatus.OK);
            }
        }
    }
};
exports.ProblemController = ProblemController;
__decorate([
    (0, common_1.Get)("pdf/:id"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)("id")),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Object]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "pdf", null);
__decorate([
    (0, common_1.Post)("list"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "query", null);
__decorate([
    (0, common_1.Post)("export"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "export", null);
__decorate([
    (0, common_1.Get)(":id"),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "info", null);
__decorate([
    (0, common_1.Post)("/"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "save", null);
__decorate([
    (0, common_1.Post)("/submit/:id"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "submit", null);
__decorate([
    (0, common_1.Delete)("/"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)("upload/:id/:type"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("file", {
        storage: problemStorage,
        fileFilter: file_upload_utils_1.imageFileFilter,
    })),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Param)("type")),
    __param(2, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String, Object]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "uploadFile", null);
__decorate([
    (0, common_1.Delete)("attachment/:type/:id/:key"),
    __param(0, (0, common_1.Param)("type")),
    __param(1, (0, common_1.Param)("id")),
    __param(2, (0, common_1.Param)("key")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, String]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "removeFile", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)("attachment/:type/:id/:key"),
    __param(0, (0, common_1.Param)("type")),
    __param(1, (0, common_1.Param)("id")),
    __param(2, (0, common_1.Param)("key")),
    __param(3, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, String, Object]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "attachment", null);
__decorate([
    (0, common_1.Get)("logs/:id"),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "logs", null);
__decorate([
    (0, common_1.Get)(":id/reason"),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "reasons", null);
__decorate([
    (0, common_1.Post)(":id/reason"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)("id")),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, create_reason_dto_1.CreateReasonDto]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "addOrModifyReason", null);
__decorate([
    (0, common_1.Delete)(":id/reason"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)("id")),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Object]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "deleteReason", null);
__decorate([
    (0, common_1.Put)(":id/reason"),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, reason_entity_1.Reason]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "updateReason", null);
__decorate([
    (0, common_1.Post)(":id/reason-detail"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("file", {
        storage: exports.reasonStorage,
    })),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, reason_detail_entity_1.ReasonDetail, Object]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "saveOrUpdateReasonDetail", null);
__decorate([
    (0, common_1.Delete)(":id/reason-detail"),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "deleteReasonDetail", null);
__decorate([
    (0, common_1.Post)("sendMails"),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "sendMails", null);
__decorate([
    (0, schedule_1.Cron)("0 0 8 * * *"),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "handleCron", null);
__decorate([
    (0, common_1.Get)("download/tpl"),
    __param(0, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "getProblemTemplate", null);
__decorate([
    (0, common_1.Post)("data/import"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("file", {
        storage: (0, multer_1.memoryStorage)(),
    })),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __param(2, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], ProblemController.prototype, "importData", null);
exports.ProblemController = ProblemController = __decorate([
    (0, common_1.Controller)("problem"),
    __metadata("design:paramtypes", [problem_service_1.ProblemService,
        problem_operate_log_service_1.ProblemOperateLogService,
        reason_service_1.ReasonService,
        reason_config_service_1.ReasonConfigService,
        reason_detail_service_1.ReasonDetailService,
        mail_service_1.MailService,
        code_rule_service_1.CodeRuleService,
        dictionary_service_1.DictionaryService,
        user_service_1.UserService,
        puppeteer_core_1.Browser])
], ProblemController);
//# sourceMappingURL=problem.controller.js.map