import { JwtService } from "@nestjs/jwt";
import { BaseRemoteService } from "src/auth/base-remote.service";
import { Payload } from "src/auth/jwt.strategy";
import { User, UserService } from "../user/user.service";
export declare class AuthService extends BaseRemoteService {
    private userService;
    private jwtService;
    constructor(userService: UserService, jwtService: JwtService);
    encryption(token: string, key?: string): string;
    isTokenValid(payload: Payload): Promise<boolean>;
    getRealToken(secretKey: string): Promise<string>;
    getToken(authKey: string): Promise<string>;
    login(token: string): Promise<{
        token: string;
        user: Partial<User>;
    }>;
    logout(payload: Payload): Promise<string>;
    changeLang(payload: Payload, code: string): Promise<string | true>;
}
