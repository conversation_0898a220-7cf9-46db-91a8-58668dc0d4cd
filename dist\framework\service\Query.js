"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Query = void 0;
const typeorm_1 = require("typeorm");
const OperatorType = {
    LIKE: (value) => (0, typeorm_1.Like)(`%${value}%`),
    NOT_LIKE: (value) => (0, typeorm_1.Not)((0, typeorm_1.Like)(`%${value}%`)),
    START_WITH: (value) => (0, typeorm_1.Like)(`${value}%`),
    NOT_START_WITH: (value) => (0, typeorm_1.Not)((0, typeorm_1.Like)(`${value}%`)),
    END_WITH: (value) => (0, typeorm_1.Like)(`%${value}`),
    NOT_END_WITH: (value) => (0, typeorm_1.Not)((0, typeorm_1.Like)(`%${value}`)),
    IN: typeorm_1.In,
    NIN: (value) => (0, typeorm_1.Not)((0, typeorm_1.In)(value)),
    GT: typeorm_1.More<PERSON>han,
    GTE: typeorm_1.MoreThanOrEqual,
    LT: typeorm_1.<PERSON><PERSON>han,
    LTE: typeorm_1.LessThanOrEqual,
    BETWEEN: ([from, to]) => (0, typeorm_1.Between)(from, to),
    NOT_BETWEEN: ([from, to]) => (0, typeorm_1.Not)((0, typeorm_1.Between)(from, to)),
    RANGE_OPEN: ([from, to]) => (0, typeorm_1.And)((0, typeorm_1.MoreThan)(from), (0, typeorm_1.LessThan)(to)),
    NOT_RANGE_OPEN: ([from, to]) => (0, typeorm_1.Not)((0, typeorm_1.And)((0, typeorm_1.MoreThan)(from), (0, typeorm_1.LessThan)(to))),
    RANGE_CLOSE: ([from, to]) => (0, typeorm_1.And)((0, typeorm_1.MoreThanOrEqual)(from), (0, typeorm_1.LessThanOrEqual)(to)),
    NOT_RANGE_CLOSE: ([from, to]) => (0, typeorm_1.Not)((0, typeorm_1.And)((0, typeorm_1.MoreThanOrEqual)(from), (0, typeorm_1.LessThanOrEqual)(to))),
    RANGE_OPEN_CLOSE: ([from, to]) => (0, typeorm_1.And)((0, typeorm_1.MoreThan)(from), (0, typeorm_1.LessThanOrEqual)(to)),
    NOT_RANGE_OPEN_CLOSE: ([from, to]) => (0, typeorm_1.Not)((0, typeorm_1.And)((0, typeorm_1.MoreThan)(from), (0, typeorm_1.LessThanOrEqual)(to))),
    RANGE_CLOSE_OPEN: ([from, to]) => (0, typeorm_1.And)((0, typeorm_1.MoreThanOrEqual)(from), (0, typeorm_1.LessThan)(to)),
    NOT_RANGE_CLOSE_OPEN: ([from, to]) => (0, typeorm_1.Not)((0, typeorm_1.And)((0, typeorm_1.MoreThanOrEqual)(from), (0, typeorm_1.LessThan)(to))),
};
class Query {
    skip;
    take;
    order;
    where;
    relations;
    _isPageable = false;
    constructor(request = {}) {
        const { page, operations = {}, params } = request;
        if (page) {
            const { no, size, orders } = page;
            if (typeof no !== "undefined" && typeof size !== "undefined") {
                this.skip = no * size;
                this.take = size;
                this._isPageable = true;
            }
            if (orders?.length) {
                const _order = {};
                for (const { property, direction = "ASC" } of orders) {
                    _order[property] = direction;
                }
                this.order = _order;
            }
        }
        const where = {};
        const hasCondition = true;
        if (params) {
            for (const [name, value] of Object.entries(params)) {
                if ((Array.isArray(value) && value.length === 0) ||
                    typeof value === "undefined" ||
                    value === null ||
                    !value.length) {
                    continue;
                }
                let operator = operations[name];
                if (!operator) {
                    if (Array.isArray(value) || typeof value !== "string") {
                        operator = "IN";
                    }
                    else {
                        operator = "LIKE";
                    }
                }
                where[name] = OperatorType[operator.toUpperCase()](value);
            }
            if (hasCondition) {
                this.where = where;
            }
        }
    }
    get pageable() {
        return this._isPageable;
    }
}
exports.Query = Query;
//# sourceMappingURL=Query.js.map