"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeRuleController = void 0;
const common_1 = require("@nestjs/common");
const code_rule_service_1 = require("./code-rule.service");
let CodeRuleController = class CodeRuleController {
    service;
    constructor(service) {
        this.service = service;
    }
    async query(request) {
        return await this.service.query(request);
    }
    async save(entity) {
        return await this.service.saveOrUpdate(entity);
    }
    async remove(request) {
        await this.service.delete(request.ids);
    }
    async duplicate(entity) {
        return this.service.duplicate(entity);
    }
};
exports.CodeRuleController = CodeRuleController;
__decorate([
    (0, common_1.Post)("list"),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CodeRuleController.prototype, "query", null);
__decorate([
    (0, common_1.Post)("/"),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CodeRuleController.prototype, "save", null);
__decorate([
    (0, common_1.Delete)("/"),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CodeRuleController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)("duplicate"),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CodeRuleController.prototype, "duplicate", null);
exports.CodeRuleController = CodeRuleController = __decorate([
    (0, common_1.Controller)("code-rule"),
    __metadata("design:paramtypes", [code_rule_service_1.CodeRuleService])
], CodeRuleController);
//# sourceMappingURL=code-rule.controller.js.map