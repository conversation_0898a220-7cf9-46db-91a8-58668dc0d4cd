"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var PuppeteerModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PuppeteerModule = void 0;
const common_1 = require("@nestjs/common");
const puppeteer_core_module_1 = require("./puppeteer-core.module");
const puppeteer_constants_1 = require("./puppeteer.constants");
const puppeteer_providers_1 = require("./puppeteer.providers");
let PuppeteerModule = PuppeteerModule_1 = class PuppeteerModule {
    static forRoot(options) {
        return {
            module: PuppeteerModule_1,
            global: options?.isGlobal,
            imports: [puppeteer_core_module_1.PuppeteerCoreModule.forRoot(options)],
        };
    }
    static forFeature(pages, browser = puppeteer_constants_1.DEFAULT_BROWSER_NAME) {
        const providers = (0, puppeteer_providers_1.createPuppeteerProviders)(pages, browser);
        return {
            module: PuppeteerModule_1,
            providers: providers,
            exports: providers,
        };
    }
    static forRootAsync(options) {
        return {
            module: PuppeteerModule_1,
            global: options?.isGlobal,
            imports: [puppeteer_core_module_1.PuppeteerCoreModule.forRootAsync(options)],
        };
    }
};
exports.PuppeteerModule = PuppeteerModule;
exports.PuppeteerModule = PuppeteerModule = PuppeteerModule_1 = __decorate([
    (0, common_1.Module)({})
], PuppeteerModule);
//# sourceMappingURL=puppeteer.module.js.map