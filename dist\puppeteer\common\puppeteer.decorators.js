"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InjectPage = exports.InjectBrowser = void 0;
const common_1 = require("@nestjs/common");
const puppeteer_utils_1 = require("./puppeteer.utils");
const InjectBrowser = (browserName) => (0, common_1.Inject)((0, puppeteer_utils_1.getBrowserToken)(browserName));
exports.InjectBrowser = InjectBrowser;
const InjectPage = (pageName, browserName) => (0, common_1.Inject)((0, puppeteer_utils_1.getPageToken)(pageName, browserName));
exports.InjectPage = InjectPage;
//# sourceMappingURL=puppeteer.decorators.js.map