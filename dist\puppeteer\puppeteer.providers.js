"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createPuppeteerProviders = createPuppeteerProviders;
const common_1 = require("./common");
function createPuppeteerProviders(pages, browser) {
    return (pages || []).map(page => ({
        provide: (0, common_1.getPageToken)(page, browser),
        useFactory: async (browser) => {
            return await browser.newPage();
        },
        inject: [(0, common_1.getBrowserToken)(browser)],
    }));
}
//# sourceMappingURL=puppeteer.providers.js.map