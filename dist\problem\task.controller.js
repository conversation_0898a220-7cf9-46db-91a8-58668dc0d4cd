"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const node_fs_1 = require("node:fs");
const path_1 = require("path");
const public_decorator_1 = require("../auth/public.decorator");
const mail_service_1 = require("../mail/mail.service");
const user_service_1 = require("../user/user.service");
const file_upload_utils_1 = require("../utils/file-upload.utils");
const constant_1 = require("./entities/constant");
const reason_config_entity_1 = require("./entities/reason-config.entity");
const reason_entity_1 = require("./entities/reason.entity");
const problem_operate_log_service_1 = require("./problem-operate-log.service");
const problem_controller_1 = require("./problem.controller");
const problem_service_1 = require("./problem.service");
const reason_detail_service_1 = require("./reason-detail.service");
const reason_service_1 = require("./reason.service");
let TaskController = class TaskController {
    service;
    logService;
    reasonService;
    reasonDetailService;
    mailService;
    userService;
    constructor(service, logService, reasonService, reasonDetailService, mailService, userService) {
        this.service = service;
        this.logService = logService;
        this.reasonService = reasonService;
        this.reasonDetailService = reasonDetailService;
        this.mailService = mailService;
        this.userService = userService;
    }
    async query(req, request) {
        let status;
        let node;
        if (Array.isArray(request.params?.node) &&
            request.params?.node?.length > 0) {
            node = [...request.params.node];
            request.params.node = [];
        }
        if (!!request.params?.processStatus?.length) {
            status = request.params.processStatus[0];
            request.params.processStatus = "";
        }
        const { builder, pageable } = this.service.transfer(request);
        const subQuery = builder
            .subQuery()
            .select("1")
            .from(reason_config_entity_1.ReasonConfig, "prc")
            .innerJoin("prc.reason", "pr")
            .where("prc.state in (:...states) and pr.stateIdx = prc.stateIdx and prc.ownerId = :ownerId", {
            ownerId: req.payload.id,
            states: [constant_1.NodeState.ANALYZE, constant_1.NodeState.VALIDATE],
        })
            .andWhere("pr.delete is false")
            .andWhere(`pr.problem.id = ${builder.alias}.id`);
        if (!!node?.length) {
            subQuery.andWhere("pr.state in (:...node)", { node });
        }
        builder.andWhereExists(subQuery);
        builder.andWhere(`${builder.alias}.status not in (:...statuses)`, {
            statuses: [
                constant_1.ProblemStatus.DRAFT,
                constant_1.ProblemStatus.OBSOLETE,
                constant_1.ProblemStatus.CLOSED,
            ],
        });
        builder.addOrderBy(`${builder.alias}.id`, "DESC");
        return await this.service.doQuery(builder, pageable);
    }
    hasNoPermission(reason, currentUserId) {
        const config = reason.configs.find(config => config.state === reason.state);
        return !config || config.ownerId !== currentUserId;
    }
    async reasons(req, id) {
        const reasons = await this.reasonService.findByProblemId(id);
        return reasons.filter(item => !item.delete &&
            item.configs.some(config => config.ownerId === req.payload.id &&
                (config.state === constant_1.NodeState.ANALYZE ||
                    config.state === constant_1.NodeState.VALIDATE)));
    }
    async updateReason(req, id, entity) {
        const target = await this.reasonService.get(id, { configs: true });
        if ((target.state !== constant_1.NodeState.ANALYZE &&
            target.state !== constant_1.NodeState.VALIDATE) ||
            this.hasNoPermission(target, req.payload.id)) {
            throw new common_1.ForbiddenException("当前用户不能操作执行该操作");
        }
        if (target.state === constant_1.NodeState.ANALYZE) {
            target.improvement = entity.improvement;
            target.unqualityType = entity.unqualityType;
            target.unqualityCode = entity.unqualityCode;
            target.estimatedFinishOn = entity.estimatedFinishOn;
        }
        else if (target.state === constant_1.NodeState.VALIDATE) {
            target.validateResult = entity.validateResult;
        }
        return this.reasonService.execute(manager => manager.save(this.reasonService.target, target));
    }
    async transferReason(req, id, entity) {
        const target = await this.reasonService.get(entity.id, { configs: true });
        if ((target.state !== constant_1.NodeState.ANALYZE &&
            target.state !== constant_1.NodeState.VALIDATE) ||
            this.hasNoPermission(target, req.payload.id)) {
            throw new common_1.ForbiddenException("当前用户不能操作执行该操作");
        }
        const result = await this.reasonService.execute(async (manager) => {
            if (target.state === constant_1.NodeState.ANALYZE) {
                target.configs = [
                    ...entity.configs.map(config => ({
                        ...config,
                        stateIdx: constant_1.nodes.find(node => node.state === config.state)?.index ?? -1,
                    })),
                    ...target.configs.filter(config => config.state !== constant_1.NodeState.ANALYZE &&
                        config.state !== constant_1.NodeState.ANALYZE_AUDIT &&
                        config.copy !== "ANALYZE"),
                ];
            }
            else if (target.state === constant_1.NodeState.VALIDATE) {
                target.configs = [
                    ...target.configs.filter(config => config.state !== constant_1.NodeState.VALIDATE &&
                        config.state !== constant_1.NodeState.VALIDATE_AUDIT &&
                        config.state !== constant_1.NodeState.CQE_AUDIT &&
                        config.copy !== "VALIDATE"),
                    ...entity.configs.map(config => ({
                        ...config,
                        stateIdx: constant_1.nodes.find(node => node.state === config.state)?.index ?? -1,
                    })),
                ];
            }
            return manager.save(this.reasonService.target, target);
        });
        const problem = await this.service.get(id);
        if (!!result.todoId) {
            await this.userService.processedTodo(req.payload.token, [result.todoId]);
        }
        const results = await this.userService.addTodo(req.payload.token, [result], problem, result.state);
        if (results?.length) {
            await this.reasonService.execute(async (manager) => {
                return manager.save(this.reasonService.target, results);
            });
        }
        this.mailService.sendEmail([result], result.state, problem);
        return result;
    }
    async get(id) {
        return this.service.get(id);
    }
    async saveOrUpdateReasonDetail(req, id, body, file) {
        const reason = await this.reasonService.get(id, { configs: true });
        if (reason.status === constant_1.ReasonStatus.CLOSED ||
            reason.status === constant_1.ReasonStatus.FOLLOW) {
            throw new common_1.ForbiddenException("流程正在进行中或已关闭,非法操作");
        }
        if (reason.state !== constant_1.NodeState.ANALYZE ||
            this.hasNoPermission(reason, req.payload.id)) {
            throw new common_1.ForbiddenException("当前用户不能操作执行该操作");
        }
        const entity = JSON.parse(body.entity);
        if (!entity.id) {
            entity.reason = { id };
        }
        if (!!file) {
            entity.attachment = {
                bucket: file.destination,
                extension: (0, path_1.extname)(file.originalname),
                filename: file.originalname,
                key: (0, path_1.parse)(file.filename).name,
                size: file.size,
                type: file.mimetype,
            };
        }
        return this.reasonDetailService.saveOrUpdate(entity);
    }
    async attachment(id, key, res) {
        const { attachment } = await this.reasonDetailService.get(id);
        res.sendFile((0, path_1.join)(attachment.bucket, `${attachment.key}${attachment.extension}`), {
            headers: {
                "Content-Disposition": `attachment; filename*=${encodeURIComponent(attachment.filename)}`,
            },
        });
    }
    async download(id, res) {
        const { attachment } = await this.reasonDetailService.get(id);
        res.sendFile((0, path_1.join)(attachment.bucket, `${attachment.key}${attachment.extension}`), {
            headers: {
                "Content-Disposition": `attachment; filename*=${encodeURIComponent(attachment.filename)}`,
            },
        });
    }
    async deleteReasonDetail(req, id, request) {
        const reason = await this.reasonService.get(id, { configs: true });
        if (reason.status === constant_1.ReasonStatus.CLOSED ||
            reason.status === constant_1.ReasonStatus.FOLLOW) {
            throw new common_1.ForbiddenException("流程正在进行中或已关闭,非法操作");
        }
        if (reason.state !== constant_1.NodeState.ANALYZE ||
            this.hasNoPermission(reason, req.payload.id)) {
            throw new common_1.ForbiddenException("当前用户不能操作执行该操作");
        }
        const details = await this.reasonDetailService.findByReasonId(id);
        const attachments = [];
        const detailIds = details
            .filter(entity => request.ids.indexOf(entity.id) > -1)
            .map(entity => {
            if (!!entity.attachment) {
                attachments.push(entity.attachment);
            }
            return entity.id;
        });
        await this.reasonDetailService.delete(detailIds);
        for (const attachment of attachments) {
            try {
                (0, node_fs_1.unlinkSync)((0, path_1.join)(attachment.bucket, `${attachment.key}${attachment.extension}`));
            }
            catch { }
        }
    }
    async submit(req, id, request) {
        if (typeof request?.approved !== "undefined") {
            const reason = await this.reasonService.get(id, {
                problem: true,
                configs: true,
            });
            if (reason.state !== constant_1.NodeState.VALIDATE ||
                this.hasNoPermission(reason, req.payload.id)) {
                throw new common_1.ForbiddenException("当前用户不能操作执行该操作");
            }
            let log;
            if (!request.approved) {
                log = this.logService.repository.create({
                    problem: { id: reason.problem.id },
                    operatorId: req.payload.id,
                    operatorName: req.payload.name,
                    action: {
                        key: "problem.action.reject_analyze_by_validate",
                        message: `驳回了原因分析和行动计划`,
                        params: { reason: request.remark },
                    },
                });
                reason.state = constant_1.NodeState.ANALYZE;
                reason.stateIdx = 0;
                reason.status = constant_1.ReasonStatus.REJECTED;
                reason.remark = request.remark;
            }
            else {
                const node = constant_1.nodes.find(node => node.state === reason.state);
                if (!node.next) {
                    throw new common_1.ForbiddenException("非法的流程节点");
                }
                log = this.logService.repository.create({
                    problem: { id: reason.problem.id },
                    operatorId: req.payload.id,
                    operatorName: req.payload.name,
                    action: {
                        key: "problem.action.approve_validate",
                        message: `批准了效果验证`,
                        params: { reason: request.remark },
                    },
                });
                reason.remark = null;
                reason.state = node.next;
                reason.stateIdx = constant_1.nodes.find(item => item.state === node.next).index;
                reason.validateOn = new Date();
            }
            const result = await this.reasonService.execute(manager => {
                manager.save(this.logService.target, log);
                return manager.save(this.reasonService.target, reason);
            });
            const problem = await this.service.get(reason.problem.id);
            if (!!result.todoId) {
                await this.userService.processedTodo(req.payload.token, [result.todoId], request.approved ? "1" : "2");
            }
            const results = await this.userService.addTodo(req.payload.token, [result], problem, result.state);
            if (results?.length) {
                await this.reasonService.execute(async (manager) => {
                    return manager.save(this.reasonService.target, results);
                });
            }
            this.mailService.sendEmail([result], result.state, problem);
            return result;
        }
        else {
            const reason = await this.reasonService.get(id, {
                problem: true,
                configs: true,
            });
            if (reason.state !== constant_1.NodeState.ANALYZE &&
                this.hasNoPermission(reason, req.payload.id)) {
                throw new common_1.ForbiddenException("当前用户不能操作执行该操作");
            }
            if (reason.status === constant_1.ReasonStatus.OPEN ||
                reason.status === constant_1.ReasonStatus.REJECTED) {
                reason.status = constant_1.ReasonStatus.FOLLOW;
            }
            const node = constant_1.nodes.find(node => node.state === reason.state);
            if (!node.next) {
                throw new common_1.ForbiddenException("非法的流程节点");
            }
            reason.state = node.next;
            reason.stateIdx = constant_1.nodes.find(item => item.state === node.next).index;
            const log = this.logService.repository.create({
                problem: { id: reason.problem.id },
                operatorId: req.payload.id,
                operatorName: req.payload.name,
                action: reason.state === constant_1.NodeState.ANALYZE_AUDIT
                    ? {
                        key: "problem.action.submit_analyze",
                        message: "提交了原因分析和行动计划",
                    }
                    : {
                        key: "problem.action.submit_validate",
                        message: "提交了效果验证",
                    },
            });
            const problem = await this.service.get(reason.problem.id);
            const result = await this.reasonService.execute(manager => {
                manager.save(this.logService.target, log);
                if (problem.status === constant_1.ProblemStatus.NEW) {
                    problem.status = constant_1.ProblemStatus.PROCESSING;
                    manager.save(this.service.target, problem);
                }
                return manager.save(this.reasonService.target, reason);
            });
            if (!!result.todoId) {
                await this.userService.processedTodo(req.payload.token, [
                    result.todoId,
                ]);
            }
            const results = await this.userService.addTodo(req.payload.token, [result], problem, result.state);
            if (results?.length) {
                await this.reasonService.execute(async (manager) => {
                    return manager.save(this.reasonService.target, results);
                });
            }
            this.mailService.sendEmail([result], result.state, problem);
            return result;
        }
    }
};
exports.TaskController = TaskController;
__decorate([
    (0, common_1.Post)("list"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TaskController.prototype, "query", null);
__decorate([
    (0, common_1.Get)(":id/reason"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], TaskController.prototype, "reasons", null);
__decorate([
    (0, common_1.Put)(":id/reason"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)("id")),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, reason_entity_1.Reason]),
    __metadata("design:returntype", Promise)
], TaskController.prototype, "updateReason", null);
__decorate([
    (0, common_1.Post)(":id/transfer"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)("id")),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, reason_entity_1.Reason]),
    __metadata("design:returntype", Promise)
], TaskController.prototype, "transferReason", null);
__decorate([
    (0, common_1.Get)(":id"),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], TaskController.prototype, "get", null);
__decorate([
    (0, common_1.Post)(":id/reason-detail"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("file", {
        storage: problem_controller_1.reasonStorage,
        fileFilter: file_upload_utils_1.fileNameEncodingFilter,
    })),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)("id")),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Object, Object]),
    __metadata("design:returntype", Promise)
], TaskController.prototype, "saveOrUpdateReasonDetail", null);
__decorate([
    (0, common_1.Get)("attachment/:id/:key"),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Param)("key")),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String, Object]),
    __metadata("design:returntype", Promise)
], TaskController.prototype, "attachment", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)("download/:id"),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], TaskController.prototype, "download", null);
__decorate([
    (0, common_1.Delete)(":id/reason-detail"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)("id")),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Object]),
    __metadata("design:returntype", Promise)
], TaskController.prototype, "deleteReasonDetail", null);
__decorate([
    (0, common_1.Post)("submit/:id"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)("id")),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Object]),
    __metadata("design:returntype", Promise)
], TaskController.prototype, "submit", null);
exports.TaskController = TaskController = __decorate([
    (0, common_1.Controller)("task"),
    __metadata("design:paramtypes", [problem_service_1.ProblemService,
        problem_operate_log_service_1.ProblemOperateLogService,
        reason_service_1.ReasonService,
        reason_detail_service_1.ReasonDetailService,
        mail_service_1.MailService,
        user_service_1.UserService])
], TaskController);
//# sourceMappingURL=task.controller.js.map