import { BaseRemoteService } from "src/auth/base-remote.service";
import { NodeState } from "src/problem/entities/constant";
import { Problem } from "src/problem/entities/problem.entity";
import { Reason } from "src/problem/entities/reason.entity";
export type User = {
    id: number;
    username: string;
    sub: number;
    password?: string;
    isDelete: number;
    jobNum: string;
    name: string;
    avatar?: string;
    tel?: string;
    email?: string;
    status?: string;
    auditStatus: string;
    reason?: string;
    postId: number;
    deptId: number;
    roleId: string[];
    rank: any;
    leader: number;
    indirectLeader?: number;
    languageCode?: string;
    postName: string;
};
export type Department = {
    id: number;
    parentId?: number;
    sort: number;
    name: string;
    leader: string;
};
export type Route = {
    id: number;
    parentId?: number;
    path: string;
    name: string;
    component: string;
    sort: number;
    meta: Record<string, any>;
    title: string;
};
export type Permission = {
    routes: Route[];
    buttons: string[];
};
export declare const MockPermission: {
    routes: {
        id: number;
        parentId: number;
        path: string;
        name: string;
        component: any;
        sort: number;
        meta: {
            isDelete: string;
            createdBy: any;
            createdTime: string;
            updatedBy: any;
            updatedTime: string;
            workflowId: any;
            parentName: any;
            appCode: string;
            perms: string;
            type: string;
            redirect: any;
            icon: any;
            title: string;
            isLink: boolean;
            isHide: boolean;
            isFull: boolean;
            isAffix: boolean;
            isKeepAlive: boolean;
            status: string;
        };
        title: string;
    }[];
    buttons: string[];
};
export declare const MockUserData: User[];
export declare const MockDepartmentData: {
    id: number;
    parentId: number;
    sort: number;
    name: string;
    leader: any;
}[];
export declare class UserService extends BaseRemoteService {
    findByToken(token: string): Promise<User | null>;
    getUserList(token: string): Promise<User[]>;
    getDepartmentList(token: string): Promise<Department[]>;
    getPermissions(token: string): Promise<Permission>;
    getLanguages(): Promise<{
        code: string;
        langData: {
            label: string;
            value: string;
        }[];
    }[]>;
    addTodo(token: string, reasons: Reason[], problem: Problem, state: NodeState): Promise<Reason[]>;
    processedTodo(token: string, ids: number[], status?: string): Promise<void>;
}
