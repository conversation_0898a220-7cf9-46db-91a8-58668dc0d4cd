import { BaseService } from "src/framework/service/base.service";
import { DataSource, Repository } from "typeorm";
import { Reason } from "./entities/reason.entity";
export declare class ReasonService extends BaseService<Reason> {
    constructor(repository: Repository<Reason>, dataSource: DataSource);
    canFinish(reason: Reason): Promise<boolean>;
    findByProblemId(id: number, relations?: string[]): Promise<Reason[]>;
    assignSkipFields(): string[];
}
