import { Relation } from "typeorm";
import { NumberIdTimeObject } from "../../framework/model/base";
import { NodeState, ReasonStatus } from "./constant";
import { Problem } from "./problem.entity";
import { ReasonConfig } from "./reason-config.entity";
import { ReasonDetail } from "./reason-detail.entity";
export declare class Reason extends NumberIdTimeObject {
    problem: Relation<Problem>;
    category: string;
    subCategory: string;
    unqualityCode: string;
    unqualityType: string;
    details: Relation<ReasonDetail>[];
    configs: Relation<ReasonConfig>[];
    improvement: string;
    validateResult: string;
    validateOn: Date;
    estimatedFinishOn: Date;
    finishOn: Date;
    status: ReasonStatus;
    state: NodeState;
    stateIdx: number;
    delete: boolean;
    deleteById: number;
    deleteByName: string;
    remark: string;
    todoId: number;
}
