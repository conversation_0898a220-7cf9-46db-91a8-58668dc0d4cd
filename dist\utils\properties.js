"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const fs_1 = __importDefault(require("fs"));
const os_1 = __importDefault(require("os"));
const path_1 = __importStar(require("path"));
const properties_reader_1 = __importDefault(require("properties-reader"));
const typeorm_naming_strategies_1 = require("typeorm-naming-strategies");
const yaml_1 = __importDefault(require("yaml"));
const entities_1 = __importDefault(require("../config/entities"));
const processArgs_1 = __importDefault(require("./processArgs"));
const CONFIG_TYPE_ERROR_MSG = "缺少配置文件类型。配置文件扩展名为'yaml' | 'yml' | 'properties' 或者运行参数指定config-type='yaml' | 'yml' | 'properties'";
const CONFIG_TYPES = new Set(["yaml", "yml", "properties"]);
const findEntity = (_path, _entities, fileName) => {
    const stats = fs_1.default.statSync(_path);
    if (stats.isDirectory()) {
        for (const file of fs_1.default.readdirSync(_path)) {
            findEntity(path_1.default.join(_path, file), _entities, file);
        }
    }
    else {
        if (fileName) {
            if (fileName.endsWith(".entity.js") || fileName.endsWith(".entity.ts")) {
                const originName = fileName.substring(0, fileName.indexOf(".entity."));
                let name = "";
                for (const part of originName.split("-")) {
                    name = `${name}${part[0].toUpperCase()}${part.substring(1)}`;
                }
                const from = `..${_path.substring(__dirname.length - "/utils".length)}`;
                _entities[name] = from.substring(0, from.lastIndexOf("."));
            }
        }
    }
};
class Config {
    _entities;
    _car_url;
    _jwt_secret;
    _jwt_expired;
    _properties;
    _base_storage_dir;
    _server_url;
    _login_page;
    _context_path;
    _mail;
    _test_copyers;
    _complete_cc;
    _port;
    _mail_template;
    _contextPath;
    _app_name;
    loadYaml(path) {
        const content = fs_1.default.readFileSync(path, "utf8");
        this._properties = yaml_1.default.parse(content);
    }
    loadProperties(path) {
        this._properties = (0, properties_reader_1.default)(path, "utf-8").getAllProperties();
    }
    getPath = () => {
        const path = processArgs_1.default.path;
        if (path === undefined) {
            const yamlPath = path_1.default.join(`${process.cwd()}`, process.env.APP === "DFX" ? "application_dfx.yaml" : "application.yaml");
            if (fs_1.default.existsSync(yamlPath)) {
                return yamlPath;
            }
            const propertiesPath = path_1.default.join(`${process.cwd()}`, "application.properties");
            if (fs_1.default.existsSync(propertiesPath)) {
                return propertiesPath;
            }
            throw new Error(`${yamlPath} 不存在\n${propertiesPath}不存在, 未指定配置文件`);
        }
        if (fs_1.default.existsSync(path)) {
            return path;
        }
        throw new Error(`${path} 不存在`);
    };
    getType = (path) => {
        const type = processArgs_1.default["config.type"];
        let _type = type;
        if (type === undefined) {
            _type = path_1.default.extname(path);
            if (!_type.startsWith(".")) {
                throw new Error(CONFIG_TYPE_ERROR_MSG);
            }
            _type = _type.substring(1);
        }
        return _type;
    };
    constructor() {
        const path = this.getPath();
        const type = this.getType(path);
        if (!CONFIG_TYPES.has(type)) {
            throw new Error(CONFIG_TYPE_ERROR_MSG);
        }
        if (type === "properties") {
            this.loadProperties(path);
        }
        else {
            this.loadYaml(path);
        }
        this._app_name = this._properties.server?.name ?? "CAR";
        this._mail_template = this._properties.server?.mailTemplate;
        this._car_url = this._properties.server?.carUrl;
        this._base_storage_dir = this._properties.storage?.base?.dir ?? os_1.default.tmpdir();
        this._entities = entities_1.default;
        this._jwt_secret =
            this._properties.server?.jwtSecret ??
                "5ca6a09ad1baf914af82f05af3f4edf2fb0750e102d0d3e5cc90a08ae0e35b6b";
        this._jwt_expired = this._properties.server?.jwtExpired ?? "1d";
        this._login_page = this._properties.server?.loginPage;
        this._server_url = this._properties.server?.url;
        this._context_path = this._properties.server?.contextPath;
        this._mail = { ...this._properties.mail };
        this._test_copyers = this._properties.server?.testCopyers ?? [];
        this._complete_cc = this._properties.server?.completeCopyers ?? [];
        this._port = this._properties.port ?? 3000;
        this._contextPath = this._properties.contextPath ?? "/api";
        if (process.env.NODE_ENV === "development") {
            const file = path_1.default.resolve(__dirname, "..", "..", "src", "config", "entities.ts");
            if (!fs_1.default.existsSync(file)) {
                this._entities = [__dirname + "/../**/*.entity{.ts,.js}"];
                const importInfo = {};
                findEntity(__dirname + "/..", importInfo);
                let source = "";
                for (const [name, from] of Object.entries(importInfo)) {
                    source = `${source}import {${name}} from '${from}'\n`;
                }
                source = `${source}export default [${Object.keys(importInfo).join(",")}]`;
                fs_1.default.writeFileSync(file, source);
            }
        }
    }
    get appName() {
        return this._app_name;
    }
    get testCopyers() {
        return this._test_copyers;
    }
    get completeCc() {
        return this._complete_cc;
    }
    get jwtSecret() {
        return this._jwt_secret;
    }
    get jwtExpired() {
        return this._jwt_expired;
    }
    get properties() {
        return this._properties;
    }
    get carUrl() {
        return this._car_url;
    }
    get baseStoreageDir() {
        return this._base_storage_dir;
    }
    get loginPage() {
        return this._login_page;
    }
    get baseApi() {
        return `${this._server_url}/${this._context_path}`;
    }
    get baseUrl() {
        return this._server_url;
    }
    get mailTemplateDir() {
        let dir;
        if (!!this._mail_template?.length) {
            dir = (0, path_1.join)(this._mail_template, "templates");
        }
        else {
            dir = (0, path_1.join)(__dirname, "../mail/templates");
        }
        common_1.Logger.log("邮件模板路径配置为:" + dir);
        return dir;
    }
    get port() {
        return this._port;
    }
    get mailConfig() {
        return this._mail;
    }
    get contextPath() {
        return this._contextPath;
    }
    getDatasource() {
        const dsConfig = this.properties?.datasource;
        const result = [];
        if (dsConfig?.length) {
            for (const cfg of dsConfig) {
                if (dsConfig.length > 1 && cfg.name === undefined) {
                    throw new Error("配置多数据源时，数据源名称不能为空");
                }
                const type = Object.keys(cfg)[0];
                result.push(typeorm_1.TypeOrmModule.forRoot({
                    type,
                    ...cfg[type],
                    namingStrategy: new typeorm_naming_strategies_1.SnakeNamingStrategy(),
                    autoLoadEntities: true,
                }));
            }
        }
        return result;
    }
}
exports.config = new Config();
//# sourceMappingURL=properties.js.map