{"name": "@fastify/formbody", "version": "7.4.0", "description": "A module for Fastify to parse x-www-form-urlencoded bodies", "main": "formbody.js", "scripts": {"codecov": "codecov", "lint": "standard | snazzy", "test": "npm run test:unit && npm run test:typescript", "test:unit": "tap", "test:typescript": "tsd"}, "precommit": ["lint", "test"], "repository": {"type": "git", "url": "git+ssh://**************/fastify/fastify-formbody.git"}, "keywords": ["fastify", "x-www-form-urlencoded"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify-formbody/issues"}, "homepage": "https://github.com/fastify/fastify-formbody#readme", "devDependencies": {"@fastify/pre-commit": "^2.0.2", "@types/node": "^18.0.0", "fastify": "^4.0.0-rc.2", "form-auto-content": "^3.0.0", "qs": "^6.5.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "tap": "^16.0.0", "tsd": "^0.24.1"}, "dependencies": {"fast-querystring": "^1.0.0", "fastify-plugin": "^4.0.0"}, "types": "types/formbody.d.ts", "publishConfig": {"access": "public"}}