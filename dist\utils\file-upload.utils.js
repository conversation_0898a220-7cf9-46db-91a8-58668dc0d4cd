"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fileNameEncodingFilter = exports.imageFileFilter = exports.FileNotMatchException = void 0;
const common_1 = require("@nestjs/common");
class FileNotMatchException extends common_1.HttpException {
    constructor(msg = "File Type Not Match") {
        super(msg, common_1.HttpStatus.OK);
    }
}
exports.FileNotMatchException = FileNotMatchException;
const imageFileFilter = (_req, file, callback) => {
    if (/^image\/.*/.test(file.mimetype) ||
        /\.(jpg|jpeg|png|gif|svg|tiff)$/.test(file.originalname)) {
        return (0, exports.fileNameEncodingFilter)(_req, file, callback);
    }
    return callback(new common_1.InternalServerErrorException("只允许图片文件"), false);
};
exports.imageFileFilter = imageFileFilter;
const fileNameEncodingFilter = (_req, file, callback) => {
    if (!/[^\u0000-\u00ff]/.test(file.originalname)) {
        file.originalname = Buffer.from(file.originalname, "latin1").toString("utf8");
    }
    return callback(null, true);
};
exports.fileNameEncodingFilter = fileNameEncodingFilter;
//# sourceMappingURL=file-upload.utils.js.map