"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProblemService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const fs_1 = require("fs");
const path_1 = require("path");
const base_service_1 = require("../framework/service/base.service");
const typeorm_2 = require("typeorm");
const constant_1 = require("./entities/constant");
const problem_operate_log_entity_1 = require("./entities/problem-operate-log.entity");
const problem_entity_1 = require("./entities/problem.entity");
const reason_config_entity_1 = require("./entities/reason-config.entity");
const reason_entity_1 = require("./entities/reason.entity");
let ProblemService = class ProblemService extends base_service_1.BaseService {
    logRepository;
    constructor(repository, dataSource, logRepository) {
        super(repository, dataSource);
        this.logRepository = logRepository;
    }
    populateQueryBuilder(builder) {
        builder.leftJoinAndSelect(`${builder.alias}.reasons`, "r", "r.delete is false");
        builder.leftJoinAndSelect(`r.configs`, "c");
    }
    async query(request) {
        let owner, node, remark;
        if (Array.isArray(request.params.node) && request.params.node.length > 0) {
            node = [...request.params.node];
            request.params.node = [];
        }
        if (!!request.params.owner?.length) {
            owner = request.params.owner;
            request.params.owner = "";
        }
        if (!!request.params.remark?.length) {
            remark = request.params.remark;
            request.params.remark = "";
        }
        const { builder, pageable } = this.transfer(request);
        if (!!node?.length) {
            builder.andWhereExists(builder
                .subQuery()
                .select("1")
                .from(reason_entity_1.Reason, "pr")
                .where("pr.state in (:...node)", { node })
                .andWhere("pr.delete is false")
                .andWhere(`pr.problem.id = ${builder.alias}.id`));
        }
        if (!!owner?.length) {
            builder.andWhereExists(builder
                .subQuery()
                .select("1")
                .from(reason_config_entity_1.ReasonConfig, "prc")
                .innerJoin("prc.reason", "pr")
                .where(`
            CASE WHEN pr.state in (:...states) THEN prc.state = pr.state and prc.ownerName like :owner 
                 WHEN pr.state = :state1 THEN 1=2 END
            `, {
                owner: `%${owner}%`,
                states: [
                    constant_1.NodeState.ANALYZE,
                    constant_1.NodeState.VALIDATE,
                    constant_1.NodeState.ANALYZE_AUDIT,
                    constant_1.NodeState.VALIDATE_AUDIT,
                    constant_1.NodeState.CQE_AUDIT,
                ],
                state1: constant_1.NodeState.COMPLETE,
            })
                .andWhere("pr.delete is false")
                .andWhere(`pr.problem.id = ${builder.alias}.id`));
        }
        if (!!remark?.length) {
            builder.andWhereExists(builder
                .subQuery()
                .select("1")
                .from(reason_entity_1.Reason, "pr")
                .where("pr.remark like :remark", { remark: `%${remark}%` })
                .andWhere("pr.delete is false")
                .andWhere(`pr.problem.id = ${builder.alias}.id`));
        }
        builder.addOrderBy(`${builder.alias}.id`, "DESC");
        return this.doQuery(builder, pageable);
    }
    assignSkipFields() {
        return [
            ...super.assignSkipFields(),
            "creatorId",
            "creatorName",
            "status",
            "goodPartImages",
            "badPartImages",
            "reasons",
            "logs",
        ];
    }
    async preSave(entity) {
        if (!entity.id) {
            const log = this.logRepository.create({
                operatorId: entity.creatorId,
                operatorName: entity.creatorName,
                action: { key: "problem.action.create", message: "创建了问题" },
            });
            entity.logs = [log];
        }
    }
    async deleteEntities(entities, isAdmin, cb = async () => { }) {
        const toRemove = [];
        const toObsolete = [];
        entities.forEach(entity => {
            if (entity.status === constant_1.ProblemStatus.DRAFT ||
                entity.status === constant_1.ProblemStatus.CQE) {
                toRemove.push(entity);
            }
            else if (isAdmin ||
                (entity.status !== constant_1.ProblemStatus.CLOSED &&
                    entity.status !== constant_1.ProblemStatus.OBSOLETE)) {
                toObsolete.push(entity);
            }
        });
        const attachments = toRemove.flatMap(entity => (entity.goodPartImages ?? []).concat(entity.badPartImages ?? []));
        await this.execute(async (manager) => {
            if (!!toObsolete.length) {
                const logs = [];
                toObsolete.forEach(item => {
                    item.status = constant_1.ProblemStatus.OBSOLETE;
                    logs.push(this.logRepository.create({
                        problem: { id: item.id },
                        operatorId: item.creatorId,
                        operatorName: item.creatorName,
                        action: { key: "problem.action.obsolete", message: "作废了问题" },
                    }));
                });
                await cb(toObsolete);
                await manager.save(this.logRepository.target, logs);
                await manager.save(this.target, toObsolete);
            }
            if (!!toRemove.length) {
                await manager
                    .createQueryBuilder()
                    .delete()
                    .from(this.target, "entity")
                    .whereInIds(toRemove.map(item => item.id))
                    .execute();
            }
        });
        for (const attachment of attachments) {
            (0, fs_1.unlinkSync)((0, path_1.join)(attachment.bucket, `${attachment.key}${attachment.extension}`));
        }
    }
    async delete(ids) {
        const entities = await this.repository.findBy({ id: (0, typeorm_2.In)(ids) });
        this.deleteEntities(entities, false);
    }
};
exports.ProblemService = ProblemService;
exports.ProblemService = ProblemService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(problem_entity_1.Problem)),
    __param(2, (0, typeorm_1.InjectRepository)(problem_operate_log_entity_1.ProblemOperateLog)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource,
        typeorm_2.Repository])
], ProblemService);
//# sourceMappingURL=problem.service.js.map