"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const schedule_1 = require("@nestjs/schedule");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const auth_module_1 = require("./auth/auth.module");
const code_rule_module_1 = require("./code-rule/code-rule.module");
const datasource_1 = require("./config/datasource");
const dashboard_module_1 = require("./dashboard/dashboard.module");
const dictionary_module_1 = require("./dictionary/dictionary.module");
const mail_module_1 = require("./mail/mail.module");
const problem_module_1 = require("./problem/problem.module");
const puppeteer_1 = require("./puppeteer");
const user_module_1 = require("./user/user.module");
const properties_1 = require("./utils/properties");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            platform_express_1.MulterModule.register({
                dest: properties_1.config.baseStoreageDir,
            }),
            datasource_1.DatasourceConfig,
            user_module_1.UserModule,
            auth_module_1.AuthModule,
            dictionary_module_1.DictionaryModule,
            code_rule_module_1.CodeRuleModule,
            problem_module_1.ProblemModule,
            dashboard_module_1.DashboardModule,
            mail_module_1.MailModule,
            schedule_1.ScheduleModule.forRoot(),
            puppeteer_1.PuppeteerModule.forRoot({
                executablePath: "./chrome/chrome",
                args: [
                    "--disable-gpu",
                    "--disable-dev-shm-usage",
                    "--disable-setuid-sandbox",
                    "--no-first-run",
                    "--no-sandbox",
                    "--no-zygote",
                    "--single-process",
                ],
            }),
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map