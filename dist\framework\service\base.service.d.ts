import { DataSource, FindOptionsRelationByString, FindOptionsRelations, Repository, SelectQueryBuilder } from "typeorm";
import { IBaseService, Page } from "./IBase.service";
import { NumberIdObject } from "../model/base";
import { QueryRequest } from "./Query";
import { EntityManager } from "typeorm/entity-manager/EntityManager";
export declare abstract class BaseService<T extends NumberIdObject> implements IBaseService<T> {
    private readonly _repository;
    private dataSource;
    protected constructor(_repository: Repository<T>, dataSource: DataSource);
    get repository(): Repository<T>;
    get target(): import("typeorm").EntityTarget<T>;
    query(request: QueryRequest): Promise<T[] | Page<T>>;
    transfer(request: QueryRequest, alias?: string): {
        builder: SelectQueryBuilder<T>;
        pageable: boolean;
    };
    populateQueryBuilder(builder: SelectQueryBuilder<T>): void;
    doQuery(builder: SelectQueryBuilder<T>, pageable: boolean): Promise<T[] | Page<T>>;
    mapperMany(data: T[]): T[];
    mapper(datum: T): T;
    assignSkipFields(): string[];
    processUpdate(source: Partial<T>, target: T): void;
    preSave(_entity: T): Promise<void>;
    saveOrUpdate(source: Partial<T>): Promise<T>;
    execute<R>(func: (entityManager: EntityManager) => Promise<R>): Promise<R>;
    delete(ids: number[]): Promise<void>;
    get(id: number, relations?: FindOptionsRelations<T> | FindOptionsRelationByString): Promise<T>;
}
