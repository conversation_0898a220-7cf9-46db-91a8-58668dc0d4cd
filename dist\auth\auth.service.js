"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const CryptoJS = __importStar(require("crypto-js"));
const base_remote_service_1 = require("./base-remote.service");
const user_service_1 = require("../user/user.service");
const constant_1 = require("./../problem/entities/constant");
let AuthService = class AuthService extends base_remote_service_1.BaseRemoteService {
    userService;
    jwtService;
    constructor(userService, jwtService) {
        super();
        this.userService = userService;
        this.jwtService = jwtService;
    }
    encryption(token, key = constant_1.PRIVATE_KEY) {
        if (!token?.length) {
            return "";
        }
        return CryptoJS.AES.encrypt(JSON.stringify({
            appCode: "car",
            token: `Bearer ${token}`,
            timestamp: Date.now(),
        }), key).toString();
    }
    async isTokenValid(payload) {
        if (payload.token.length < 10) {
            return true;
        }
        const data = await this.getToken(payload.token);
        return !!data?.length;
    }
    async getRealToken(secretKey) {
        return await this.execute("/auth/real_token", "POST", null, {
            secretKey: decodeURIComponent(secretKey),
        });
    }
    async getToken(authKey) {
        return await this.execute("/auth/get_token", "GET", authKey);
    }
    async login(token) {
        let _token = token;
        if (_token?.length > 2) {
            _token = await this.getToken(_token);
        }
        if (!_token?.length) {
            throw new common_1.UnauthorizedException();
        }
        const user = await this.userService.findByToken(_token);
        if (!user) {
            throw new common_1.UnauthorizedException();
        }
        const userPayload = {
            id: user.id,
            name: user.name,
            username: user.username,
            avatar: !user.avatar?.length ? undefined : user.avatar,
            lang: user.languageCode,
            roleId: user.roleId,
        };
        return {
            token: this.jwtService.sign({
                token,
                id: user.id,
                name: user.name,
            }, token.length <= 2 ? { expiresIn: "10y" } : {}),
            user: userPayload,
        };
    }
    async logout(payload) {
        return await this.execute("/auth/logout", "GET", payload.token);
    }
    async changeLang(payload, code) {
        if (payload.token.length < 10) {
            return true;
        }
        return await this.execute("/sys_staff/update_lang_code", "PUT", payload.token, { code });
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [user_service_1.UserService,
        jwt_1.JwtService])
], AuthService);
//# sourceMappingURL=auth.service.js.map