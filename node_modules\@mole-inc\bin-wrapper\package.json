{"name": "@mole-inc/bin-wrapper", "version": "8.0.1", "description": "Binary wrapper that makes your programs seamlessly available as local dependencies", "license": "MIT", "type": "module", "exports": {"import": "./index.js", "require": "./index.cjs"}, "repository": {"type": "git", "url": "git+https://github.com/mole-inc/bin-wrapper.git"}, "bugs": {"url": "https://github.com/mole-inc/bin-wrapper/issues"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && c8 ava", "build": "rollup -c", "coverage": "c8 report --reporter lcov", "prerelease": "pnpm run build", "release": "standard-version", "prepare": "husky install"}, "files": ["index.js", "index.cjs"], "publishConfig": {"access": "public"}, "keywords": ["bin", "check", "local", "wrapper"], "dependencies": {"bin-check": "^4.1.0", "bin-version-check": "^5.0.0", "content-disposition": "^0.5.4", "ext-name": "^5.0.0", "file-type": "^17.1.6", "filenamify": "^5.0.2", "got": "^11.8.5", "os-filter-obj": "^2.0.0"}, "devDependencies": {"@commitlint/cli": "latest", "@commitlint/config-conventional": "latest", "@rollup/plugin-alias": "^3.1.9", "@rollup/plugin-commonjs": "^22.0.0-13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.1.3", "ava": "^4.0.1", "c8": "^7.11.0", "eslint-import-resolver-webpack": "^0.13.2", "executable": "^4.1.1", "husky": "^8.0.2", "nock": "^13.2.4", "path-exists": "^5.0.0", "rimraf": "^3.0.2", "rollup": "^2.68.0", "standard-version": "latest", "tempy": "^2.0.0", "xo": "^0.48.0"}, "xo": {"rules": {"unicorn/prefer-node-protocol": "off"}}}