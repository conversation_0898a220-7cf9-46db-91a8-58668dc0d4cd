import { MailerService } from "@nestjs-modules/mailer";
import { Injectable, Logger, Optional, Inject } from "@nestjs/common";
import { <PERSON><PERSON><PERSON> } from "puppeteer-core";
import { NodeState } from "src/problem/entities/constant";
import { Problem } from "src/problem/entities/problem.entity";
import { Reason } from "src/problem/entities/reason.entity";
import { config } from "src/utils/properties";
import { ReasonStatus } from "./../problem/entities/constant";

@Injectable()
export class MailService {
  constructor(
    private mailerService: MailerService,
    @Optional() @Inject('Browser') private browser?: Browser,
  ) {}

  async sendTipEmails(
    users: { name: string; email: string; problems: Set<string> }[],
    type: "task" | "audit" = "audit",
  ) {
    const url = `${config.carUrl}${type}`;
    const action = type === "task" ? "处理" : "审批";
    users.forEach(item => {
      this.mailerService
        .sendMail({
          to: [item.email],
          cc: [...config.testCopyers],
          subject: `QMS通知：【${config.appName}】您有${item.problems.size}个问题长时间未${action}`,
          template: "./tip",
          context: {
            name: `${item.name}`,
            app: config.appName,
            message: `您有${item.problems.size}个${
              config.appName
            }问题(${Array.from(item.problems).join(",")})待${action}`,
            url,
          },
        })
        .catch(e => {
          Logger.error(e.message, e);
        });
    });
  }

  async sendEmail(
    reasons: Reason[],
    state: NodeState,
    problem: Problem,
    tip: boolean = false,
  ) {
    let image: string;

    // 如果 browser 不可用，跳过截图功能
    if (this.browser) {
      const page = await this.browser.newPage();
      await page.setViewport({ width: 1920, height: 9999 });
      try {
        await page.goto(
          `${config.carUrl}?authKey=1&redirect=/pdf/${problem.id}`,
          {
            waitUntil: "domcontentloaded",
          },
        );
        await page.waitForNavigation({ timeout: 20000 });
        await page.waitForNetworkIdle({ timeout: 20000 });
        // await page.waitForSelector(`#${problem.code}`, { timeout: 10000 });
        await page.content();
        const bodyHandler = await page.$("body");
        const { width, height } = await bodyHandler.boundingBox();
        await page.setViewport({
          width: Math.ceil(width),
          height: Math.ceil(height),
          deviceScaleFactor: 2,
        });
        image = await page.screenshot({
          fullPage: true,
          encoding: "base64",
        });
      } finally {
        await page.close();
      }
    } else {
      Logger.warn('Browser not available, skipping screenshot generation');
      image = null; // 或者设置一个默认图片
    }

    const to = [];
    const cc = [...config.testCopyers];
    if (state === NodeState.ANALYZE) {
      const redirect = encodeURIComponent(`/task?entityId=${problem.id}`);
      const url = `${config.loginPage}&redirect=${redirect}`;
      for (const reason of reasons) {
        const action =
          reason.status === ReasonStatus.REJECTED
            ? "被驳回"
            : "需要原因分析和行动计划";
        reason.configs
          .filter(
            nodeConfig =>
              nodeConfig.state === state && !!nodeConfig.ownerEmail?.length,
          )
          .forEach(nodeConfig => {
            this.mailerService
              .sendMail({
                to: [nodeConfig.ownerEmail],
                cc,
                subject: `QMS通知：【${config.appName}】${problem.code}待处理`,
                template: "./task",
                context: {
                  name: `${nodeConfig.ownerName}`,
                  app: config.appName,
                  message: `您有一个${config.appName}问题${action}`,
                  image,
                  url,
                  applier: problem.creatorName,
                },
              })
              .catch(e => {
                Logger.error(e.message, e);
              });
          });
      }
    } else if (state === NodeState.VALIDATE) {
      const redirect = encodeURIComponent(`/task?entityId=${problem.id}`);
      const url = `${config.loginPage}&redirect=${redirect}`;
      for (const reason of reasons) {
        const node = reason.configs.find(
          node => node.state === NodeState.ANALYZE,
        );
        reason.configs
          .filter(
            nodeConfig =>
              nodeConfig.state === state && !!nodeConfig.ownerEmail?.length,
          )
          .forEach(nodeConfig => {
            this.mailerService
              .sendMail({
                to: [nodeConfig.ownerEmail],
                cc,
                subject: `QMS通知：【${config.appName}】${problem.code}待处理`,
                template: "./task",
                context: {
                  name: `${nodeConfig.ownerName}`,
                  message: `您有一个${config.appName}问题需要进行效果验证`,
                  app: config.appName,
                  image,
                  url,
                  applier: `${node?.ownerName}`,
                },
              })
              .catch(e => {
                Logger.error(e.message, e);
              });
          });
      }
    } else if (state === NodeState.ANALYZE_AUDIT) {
      const redirect = encodeURIComponent(`/audit?entityId=${problem.id}`);
      const url = `${config.loginPage}&redirect=${redirect}`;
      for (const reason of reasons) {
        const node = reason.configs.find(
          node => node.state === NodeState.ANALYZE,
        );
        reason.configs
          .filter(
            nodeConfig =>
              nodeConfig.state === state && !!nodeConfig.ownerEmail?.length,
          )
          .forEach(nodeConfig => {
            this.mailerService
              .sendMail({
                to: [nodeConfig.ownerEmail],
                cc,
                subject: `QMS通知：【${config.appName}】${problem.code}${
                  tip ? "已超期,请及时审批" : "待审批"
                }`,
                template: "./task",
                context: {
                  name: `${nodeConfig.ownerName}`,
                  message: `您有一个${config.appName}问题需要进行审批`,
                  app: config.appName,
                  image,
                  url,
                  applier: `${node?.ownerName}`,
                },
              })
              .catch(e => {
                Logger.error(e.message, e);
              });
          });
      }
    } else if (state === NodeState.VALIDATE_AUDIT) {
      const redirect = encodeURIComponent(`/audit?entityId=${problem.id}`);
      const url = `${config.loginPage}&redirect=${redirect}`;
      for (const reason of reasons) {
        const node = reason.configs.find(
          node => node.state === NodeState.VALIDATE,
        );
        reason.configs
          .filter(
            nodeConfig =>
              nodeConfig.state === state && !!nodeConfig.ownerEmail?.length,
          )
          .forEach(nodeConfig => {
            this.mailerService
              .sendMail({
                to: [nodeConfig.ownerEmail],
                cc,
                subject: `QMS通知：【${config.appName}】${problem.code}${
                  tip ? "已超期,请及时审批" : "待审批"
                }`,
                template: "./task",
                context: {
                  name: `${nodeConfig.ownerName}`,
                  message: `您有一个${config.appName}问题需要进行审批`,
                  app: config.appName,
                  image,
                  url,
                  applier: `${node?.ownerName}`,
                },
              })
              .catch(e => {
                Logger.error(e.message, e);
              });
          });
      }
    } else if (state === NodeState.CQE_AUDIT) {
      const redirect = encodeURIComponent(`/audit?entityId=${problem.id}`);
      const url = `${config.loginPage}&redirect=${redirect}`;
      for (const reason of reasons) {
        const node = reason.configs.find(
          node => node.state === NodeState.VALIDATE_AUDIT,
        );
        reason.configs
          .filter(
            nodeConfig =>
              nodeConfig.state === state && !!nodeConfig.ownerEmail?.length,
          )
          .forEach(nodeConfig => {
            this.mailerService
              .sendMail({
                to: [nodeConfig.ownerEmail],
                cc,
                subject: `QMS通知：【${config.appName}】${problem.code}${
                  tip ? "已超期,请及时审批" : "待审批"
                }`,
                template: "./task",
                context: {
                  name: `${nodeConfig.ownerName}`,
                  message: `您有一个${config.appName}问题需要进行审批`,
                  app: config.appName,
                  image,
                  url,
                  applier: `${node?.ownerName}`,
                },
              })
              .catch(e => {
                Logger.error(e.message, e);
              });
          });
      }
    } else if (state === NodeState.COMPLETE) {
      reasons
        .flatMap(reason => reason.configs)
        .filter(node => !!node.ownerEmail?.length)
        .forEach(node => {
          if (
            !!node.state &&
            !to.includes(node.ownerEmail) &&
            !cc.includes(node.ownerEmail)
          ) {
            to.push(node.ownerEmail);
          } else if (
            !node.state &&
            !to.includes(node.ownerEmail) &&
            !cc.includes(node.ownerEmail)
          ) {
            cc.push(node.ownerEmail);
          }
        });
      for (const copyer of config.completeCc) {
        if (!cc.includes(copyer)) {
          cc.push(copyer);
        }
      }
      this.mailerService
        .sendMail({
          to,
          cc,
          subject: `QMS通知：【${config.appName}】问题${problem.code}已关闭`,
          template: "./notification",
          context: {
            message: `问题(${problem.code})已处理完成, 特此通知`,
            image,
          },
        })
        .catch(e => {
          Logger.error(e.message, e);
        });
    }
  }
}
