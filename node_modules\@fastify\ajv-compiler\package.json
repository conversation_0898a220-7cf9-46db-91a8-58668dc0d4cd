{"name": "@fastify/ajv-compiler", "version": "3.6.0", "description": "Build and manage the AJV instances for the fastify framework", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "directories": {"test": "test"}, "scripts": {"lint": "standard", "lint:fix": "standard --fix", "unit": "tap", "test": "npm run unit && npm run test:typescript", "test:typescript": "tsd", "ajv:compile": "ajv compile -s test/source.json -o test/validate_schema.js"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/ajv-compiler.git"}, "keywords": ["ajv", "validator", "schema", "compiler", "fastify"], "author": "<PERSON> <<EMAIL>> (https://github.com/Eomm)", "license": "MIT", "bugs": {"url": "https://github.com/fastify/ajv-compiler/issues"}, "homepage": "https://github.com/fastify/ajv-compiler#readme", "devDependencies": {"ajv-cli": "^5.0.0", "ajv-errors": "^3.0.0", "ajv-i18n": "^4.0.1", "ajv-merge-patch": "^5.0.1", "cronometro": "^3.0.1", "fastify": "^4.0.0", "require-from-string": "^2.0.2", "sanitize-filename": "^1.6.3", "standard": "^17.0.0", "tap": "^16.2.0", "tsd": "^0.31.0"}, "dependencies": {"ajv": "^8.11.0", "ajv-formats": "^2.1.1", "fast-uri": "^2.0.0"}}