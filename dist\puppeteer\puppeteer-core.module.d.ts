import { DynamicModule, OnApplicationShutdown } from "@nestjs/common";
import { ModuleRef } from "@nestjs/core";
import { PuppeteerModuleAsyncOptions, PuppeteerModuleOptions } from "./interfaces";
export declare class PuppeteerCoreModule implements OnApplicationShutdown {
    private readonly options;
    private readonly moduleRef;
    private readonly logger;
    constructor(options: PuppeteerModuleOptions, moduleRef: ModuleRef);
    static forRoot(options?: any): DynamicModule;
    static forRootAsync(options: PuppeteerModuleAsyncOptions): DynamicModule;
    onApplicationShutdown(): Promise<void>;
    private static createAsyncProviders;
    private static createAsyncOptionsProvider;
}
