"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReasonDetail = void 0;
const base_1 = require("../../framework/model/base");
const typeorm_1 = require("typeorm");
const reason_entity_1 = require("./reason.entity");
const constant_1 = require("./constant");
let ReasonDetail = class ReasonDetail extends base_1.NumberIdObject {
    reason;
    question;
    answer;
    evidence;
    attachment;
    type;
};
exports.ReasonDetail = ReasonDetail;
__decorate([
    (0, typeorm_1.ManyToOne)(() => reason_entity_1.Reason, reason => reason.details, {
        nullable: true,
        onDelete: "CASCADE",
    }),
    __metadata("design:type", reason_entity_1.Reason)
], ReasonDetail.prototype, "reason", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 1000, comment: "问题" }),
    __metadata("design:type", String)
], ReasonDetail.prototype, "question", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 1000, comment: "答案" }),
    __metadata("design:type", String)
], ReasonDetail.prototype, "answer", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 1000, comment: "证据" }),
    __metadata("design:type", String)
], ReasonDetail.prototype, "evidence", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: "json", comment: "附件" }),
    __metadata("design:type", Object)
], ReasonDetail.prototype, "attachment", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: constant_1.ReasonDetailType,
        default: constant_1.ReasonDetailType.PRODUCE,
        comment: "原因类别",
    }),
    __metadata("design:type", String)
], ReasonDetail.prototype, "type", void 0);
exports.ReasonDetail = ReasonDetail = __decorate([
    (0, typeorm_1.Entity)()
], ReasonDetail);
//# sourceMappingURL=reason-detail.entity.js.map