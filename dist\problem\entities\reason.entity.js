"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Reason = void 0;
const typeorm_1 = require("typeorm");
const base_1 = require("../../framework/model/base");
const constant_1 = require("./constant");
const problem_entity_1 = require("./problem.entity");
const reason_config_entity_1 = require("./reason-config.entity");
const reason_detail_entity_1 = require("./reason-detail.entity");
let Reason = class Reason extends base_1.NumberIdTimeObject {
    problem;
    category;
    subCategory;
    unqualityCode;
    unqualityType;
    details;
    configs;
    improvement;
    validateResult;
    validateOn;
    estimatedFinishOn;
    finishOn;
    status;
    state;
    stateIdx;
    delete;
    deleteById;
    deleteByName;
    remark;
    todoId;
};
exports.Reason = Reason;
__decorate([
    (0, typeorm_1.ManyToOne)(() => problem_entity_1.Problem, problem => problem.reasons, {
        nullable: false,
        onDelete: "CASCADE",
    }),
    __metadata("design:type", Object)
], Reason.prototype, "problem", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: "原因类别(一级)" }),
    __metadata("design:type", String)
], Reason.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: "原因类别(二级)" }),
    __metadata("design:type", String)
], Reason.prototype, "subCategory", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "不良代码" }),
    __metadata("design:type", String)
], Reason.prototype, "unqualityCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "不良类别" }),
    __metadata("design:type", String)
], Reason.prototype, "unqualityType", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => reason_detail_entity_1.ReasonDetail, detail => detail.reason, {
        cascade: true,
        orphanedRowAction: "delete",
    }),
    __metadata("design:type", Array)
], Reason.prototype, "details", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => reason_config_entity_1.ReasonConfig, config => config.reason, {
        cascade: true,
        orphanedRowAction: "delete",
    }),
    __metadata("design:type", Array)
], Reason.prototype, "configs", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: "text", comment: "行动计划" }),
    __metadata("design:type", String)
], Reason.prototype, "improvement", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: "text", comment: "验证结果" }),
    __metadata("design:type", String)
], Reason.prototype, "validateResult", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: "date" }),
    __metadata("design:type", Date)
], Reason.prototype, "validateOn", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: "date" }),
    __metadata("design:type", Date)
], Reason.prototype, "estimatedFinishOn", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: "date" }),
    __metadata("design:type", Date)
], Reason.prototype, "finishOn", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: constant_1.ReasonStatus,
        default: constant_1.ReasonStatus.OPEN,
        comment: "原因状态",
    }),
    __metadata("design:type", String)
], Reason.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: constant_1.NodeState,
        default: constant_1.NodeState.ANALYZE,
        comment: "当前节点",
    }),
    __metadata("design:type", String)
], Reason.prototype, "state", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], Reason.prototype, "stateIdx", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Reason.prototype, "delete", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Number)
], Reason.prototype, "deleteById", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Reason.prototype, "deleteByName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "驳回理由", length: 500 }),
    __metadata("design:type", String)
], Reason.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Number)
], Reason.prototype, "todoId", void 0);
exports.Reason = Reason = __decorate([
    (0, typeorm_1.Entity)()
], Reason);
//# sourceMappingURL=reason.entity.js.map