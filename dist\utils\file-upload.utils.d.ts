import { HttpException } from "@nestjs/common";
export declare class FileNotMatchException extends HttpException {
    constructor(msg?: string);
}
export declare const imageFileFilter: (_req: Express.Request, file: Express.Multer.File, callback: CallableFunction) => any;
export declare const fileNameEncodingFilter: (_req: Express.Request, file: Express.Multer.File, callback: CallableFunction) => any;
