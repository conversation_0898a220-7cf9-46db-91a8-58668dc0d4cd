"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseService = void 0;
const Query_1 = require("./Query");
const common_1 = require("@nestjs/common");
class BaseService {
    _repository;
    dataSource;
    constructor(_repository, dataSource) {
        this._repository = _repository;
        this.dataSource = dataSource;
    }
    get repository() {
        return this._repository;
    }
    get target() {
        return this.repository.target;
    }
    async query(request) {
        const query = new Query_1.Query(request);
        if (query.pageable) {
            const [data, total] = await this.repository.findAndCount(query);
            return {
                data: this.mapperMany(data),
                total,
            };
        }
        return this.mapperMany(await this.repository.find(query));
    }
    transfer(request, alias) {
        const query = new Query_1.Query(request);
        const builder = this.repository.createQueryBuilder(alias);
        if (!!query.where) {
            builder.andWhere(query.where);
        }
        if (!!query.order) {
            for (const key of Object.keys(query.order)) {
                builder.addOrderBy(`${builder.alias}.${key}`, query.order[key]);
            }
        }
        if (!!query.take) {
            builder.take(query.take);
        }
        if (!!query.skip) {
            builder.skip(query.skip);
        }
        return { builder, pageable: query.pageable };
    }
    populateQueryBuilder(builder) { }
    async doQuery(builder, pageable) {
        this.populateQueryBuilder(builder);
        const data = this.mapperMany(await builder.getMany());
        if (pageable) {
            const total = await builder.getCount();
            return {
                data,
                total,
            };
        }
        return data;
    }
    mapperMany(data) {
        return data.map(datum => this.mapper(datum));
    }
    mapper(datum) {
        return datum;
    }
    assignSkipFields() {
        return ["id", "createdAt", "updatedAt"];
    }
    processUpdate(source, target) {
        const skipFields = this.assignSkipFields();
        for (const key of Object.keys(source)) {
            if (skipFields.includes(key)) {
                continue;
            }
            target[key] = source[key];
        }
    }
    async preSave(_entity) { }
    async saveOrUpdate(source) {
        const entity = source.id
            ? await this.get(source.id)
            : this.repository.create(source);
        if (!!source.id) {
            this.processUpdate(source, entity);
        }
        return await this.execute(async (manager) => {
            await this.preSave(entity);
            return manager.save(this.target, entity);
        });
    }
    async execute(func) {
        return await this.dataSource.transaction(func);
    }
    async delete(ids) {
        await this.execute(manager => manager
            .createQueryBuilder()
            .delete()
            .from(this.repository.target, "entity")
            .whereInIds(ids)
            .execute());
    }
    async get(id, relations) {
        const where = {
            id,
        };
        const entity = !!relations
            ? await this.repository.findOne({
                where,
                relations,
            })
            : await this.repository.findOneBy(where);
        if (entity === null) {
            throw new common_1.InternalServerErrorException("Entity not found");
        }
        return this.mapper(entity);
    }
}
exports.BaseService = BaseService;
//# sourceMappingURL=base.service.js.map