import type { Response } from "express";
import { Payload } from "src/auth/jwt.strategy";
import { BatchIdRequest, QueryRequest } from "src/framework/service/Query";
import { MailService } from "src/mail/mail.service";
import { UserService } from "src/user/user.service";
import { ReasonDetail } from "./entities/reason-detail.entity";
import { Reason } from "./entities/reason.entity";
import { ProblemOperateLogService } from "./problem-operate-log.service";
import { ProblemService } from "./problem.service";
import { ReasonDetailService } from "./reason-detail.service";
import { ReasonService } from "./reason.service";
export declare class TaskController {
    private readonly service;
    private readonly logService;
    private readonly reasonService;
    private readonly reasonDetailService;
    private readonly mailService;
    private readonly userService;
    constructor(service: ProblemService, logService: ProblemOperateLogService, reasonService: ReasonService, reasonDetailService: ReasonDetailService, mailService: MailService, userService: UserService);
    query(req: {
        payload: Payload;
    }, request: QueryRequest): Promise<import("./entities/problem.entity").Problem[] | import("../framework/service/IBase.service").Page<import("./entities/problem.entity").Problem>>;
    hasNoPermission(reason: Reason, currentUserId: number): boolean;
    reasons(req: {
        payload: Payload;
    }, id: number): Promise<Reason[]>;
    updateReason(req: {
        payload: Payload;
    }, id: number, entity: Reason): Promise<Reason>;
    transferReason(req: {
        payload: Payload;
    }, id: number, entity: Reason): Promise<Reason>;
    get(id: number): Promise<import("./entities/problem.entity").Problem>;
    saveOrUpdateReasonDetail(req: {
        payload: Payload;
    }, id: number, body: {
        entity: string;
    }, file: Express.Multer.File): Promise<ReasonDetail>;
    attachment(id: number, key: string, res: Response): Promise<void>;
    download(id: number, res: Response): Promise<void>;
    deleteReasonDetail(req: {
        payload: Payload;
    }, id: number, request: BatchIdRequest): Promise<void>;
    submit(req: {
        payload: Payload;
    }, id: number, request?: {
        approved: boolean;
        remark?: string;
    }): Promise<Reason>;
}
