import { UserService } from "./../user/user.service";
import { AuthService } from "./auth.service";
import type { Payload } from "./jwt.strategy";
export declare class AuthController {
    private authService;
    private userService;
    constructor(authService: AuthService, userService: UserService);
    getToken(authKey: string): Promise<boolean>;
    logout(req: {
        payload: Payload;
    }): Promise<any>;
    login(req: {
        token?: string;
    }): Promise<any>;
    getProfile(req: {
        payload: Payload;
    }): Promise<import("./../user/user.service").User>;
    getPermissions(req: {
        payload: Payload;
    }): Promise<import("./../user/user.service").Permission>;
    getLangs(): Promise<any>;
    changeLang(req: {
        payload: Payload;
    }, code: string): Promise<string | true>;
}
