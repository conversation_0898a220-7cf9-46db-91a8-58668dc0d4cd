import type { Response } from "express";
import { Browser } from "puppeteer-core";
import type { Payload } from "src/auth/jwt.strategy";
import { DictionaryService } from "src/dictionary/dictionary.service";
import { BatchIdRequest, QueryRequest } from "src/framework/service/Query";
import { MailService } from "src/mail/mail.service";
import { UserService } from "src/user/user.service";
import { CodeRuleService } from "./../code-rule/code-rule.service";
import { CreateReasonDto } from "./dto/create-reason.dto";
import { NodeState } from "./entities/constant";
import { ProblemOperateLog } from "./entities/problem-operate-log.entity";
import { Problem } from "./entities/problem.entity";
import { ReasonDetail } from "./entities/reason-detail.entity";
import { Reason } from "./entities/reason.entity";
import { ProblemOperateLogService } from "./problem-operate-log.service";
import { ProblemService } from "./problem.service";
import { ReasonConfigService } from "./reason-config.service";
import { ReasonDetailService } from "./reason-detail.service";
import { ReasonService } from "./reason.service";
export declare const ReasonStateOptions: Record<NodeState, string>;
export declare const reasonStorage: import("multer").StorageEngine;
export declare class ProblemController {
    private readonly service;
    private readonly logService;
    private readonly reasonService;
    private readonly reasonConfigService;
    private readonly reasonDetailService;
    private readonly mailService;
    private readonly codeRuleService;
    private readonly dictionaryService;
    private readonly userService;
    private readonly browser;
    constructor(service: ProblemService, logService: ProblemOperateLogService, reasonService: ReasonService, reasonConfigService: ReasonConfigService, reasonDetailService: ReasonDetailService, mailService: MailService, codeRuleService: CodeRuleService, dictionaryService: DictionaryService, userService: UserService, browser: Browser);
    convertImageToPdf(imageBuffer: Buffer, dimensions: {
        width: number;
        height: number;
    }): Promise<Buffer>;
    pdf(req: {
        payload: Payload;
    }, id: number, res: Response): Promise<void>;
    query(req: {
        payload: Payload;
    }, request: QueryRequest): Promise<Problem[] | import("../framework/service/IBase.service").Page<Problem>>;
    export(req: {
        payload: Payload;
    }, request: QueryRequest & {
        ids?: number[];
    }, res: Response): Promise<void>;
    info(id: number): Promise<Problem>;
    save(request: {
        payload: Payload;
    }, entity: Partial<Problem>): Promise<Problem>;
    submit(request: {
        payload: Payload;
    }, id: number): Promise<Problem>;
    remove(req: {
        payload: Payload;
    }, request: BatchIdRequest): Promise<void>;
    uploadFile(id: number, type: "good" | "bad", file: Express.Multer.File): Promise<Problem>;
    removeFile(type: "good" | "bad", id: number, key: string): Promise<Problem>;
    attachment(type: "good" | "bad", id: number, key: string, res: Response): Promise<void>;
    logs(id: number): Promise<ProblemOperateLog[]>;
    reasons(id: number): Promise<Reason[]>;
    addOrModifyReason(request: {
        payload: Payload;
    }, id: number, entity: CreateReasonDto): Promise<Reason>;
    deleteReason(req: {
        payload: Payload;
    }, id: number, request: BatchIdRequest): Promise<void>;
    updateReason(id: number, entity: Reason): Promise<Reason>;
    saveOrUpdateReasonDetail(id: number, entity: ReasonDetail, file: Express.Multer.File): Promise<ReasonDetail>;
    deleteReasonDetail(id: number, request: BatchIdRequest): Promise<void>;
    sendMails(req: {
        payload: Payload;
    }): Promise<void>;
    handleCron(): Promise<void>;
    getProblemTemplate(res: Response): Promise<void>;
    importData(req: {
        payload: Payload;
    }, res: Response, file: Express.Multer.File): Promise<void>;
}
