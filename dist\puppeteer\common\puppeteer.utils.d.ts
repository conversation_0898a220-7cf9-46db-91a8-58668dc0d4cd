import { Type } from "@nestjs/common";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "puppeteer-core";
import { PuppeteerModuleOptions } from "../interfaces";
export declare function getBrowserToken(browser?: PuppeteerModuleOptions | string): string | CallableFunction | Type<Browser>;
export declare function getPageToken(page: string, browser?: PuppeteerModuleOptions | string): string | CallableFunction | Type<Page>;
export declare function getBrowserPrefix(browser?: PuppeteerModuleOptions | string): string;
