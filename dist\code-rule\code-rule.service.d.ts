import { Problem } from "src/problem/entities/problem.entity";
import { DataSource, Repository } from "typeorm";
import { BaseService } from "../framework/service/base.service";
import { CodeRule } from "./entities/code-rule.entity";
export declare class CodeRuleService extends BaseService<CodeRule> {
    constructor(repository: Repository<CodeRule>, dataSource: DataSource);
    mapper(datum: CodeRule): CodeRule;
    genCode(problem: Problem, problemRepo: Repository<Problem>): Promise<void>;
    duplicate(entity: Partial<CodeRule>): Promise<boolean>;
}
