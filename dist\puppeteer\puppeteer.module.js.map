{"version": 3, "file": "puppeteer.module.js", "sourceRoot": "", "sources": ["../../src/puppeteer/puppeteer.module.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAAuD;AAKvD,mEAA8D;AAC9D,+DAA6D;AAC7D,+DAAiE;AAG1D,IAAM,eAAe,uBAArB,MAAM,eAAe;IAC1B,MAAM,CAAC,OAAO,CAAC,OAAgC;QAC7C,OAAO;YACL,MAAM,EAAE,iBAAe;YACvB,MAAM,EAAE,OAAO,EAAE,QAAQ;YACzB,OAAO,EAAE,CAAC,2CAAmB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SAChD,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,UAAU,CACf,KAAe,EACf,UAA2C,0CAAoB;QAE/D,MAAM,SAAS,GAAG,IAAA,8CAAwB,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAE3D,OAAO;YACL,MAAM,EAAE,iBAAe;YACvB,SAAS,EAAE,SAAS;YACpB,OAAO,EAAE,SAAS;SACnB,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,OAAoC;QACtD,OAAO;YACL,MAAM,EAAE,iBAAe;YACvB,MAAM,EAAE,OAAO,EAAE,QAAQ;YACzB,OAAO,EAAE,CAAC,2CAAmB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;SACrD,CAAC;IACJ,CAAC;CACF,CAAA;AA7BY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,eAAM,EAAC,EAAE,CAAC;GACE,eAAe,CA6B3B"}