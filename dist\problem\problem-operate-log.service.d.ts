import { BaseService } from "../framework/service/base.service";
import { DataSource, Repository } from "typeorm";
import { ProblemOperateLog } from "./entities/problem-operate-log.entity";
export declare class ProblemOperateLogService extends BaseService<ProblemOperateLog> {
    constructor(repository: Repository<ProblemOperateLog>, dataSource: DataSource);
    findByProblemId(id: number): Promise<ProblemOperateLog[]>;
}
