import { Module } from "@nestjs/common";
import { MulterModule } from "@nestjs/platform-express";
import { ScheduleModule } from "@nestjs/schedule";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { AuthModule } from "./auth/auth.module";
import { CodeRuleModule } from "./code-rule/code-rule.module";
import { DatasourceConfig } from "./config/datasource";
import { DashboardModule } from "./dashboard/dashboard.module";
import { DictionaryModule } from "./dictionary/dictionary.module";
import { MailModule } from "./mail/mail.module";
import { ProblemModule } from "./problem/problem.module";
import { PuppeteerModule } from "./puppeteer";
import { UserModule } from "./user/user.module";
import { config } from "./utils/properties";

@Module({
  imports: [
    MulterModule.register({
      dest: config.baseStoreageDir,
    }),
    DatasourceConfig,
    UserModule,
    AuthModule,
    DictionaryModule,
    CodeRuleModule,
    ProblemModule,
    DashboardModule,
    MailModule,
    ScheduleModule.forRoot(),
    // 暂时注释掉 Puppeteer 模块，避免启动问题
    // PuppeteerModule.forRoot({
    //   // headless: false,
    //   channel: 'chrome', // 使用系统安装的 Chrome
    //   args: [
    //     "--disable-gpu",
    //     "--disable-dev-shm-usage",
    //     "--disable-setuid-sandbox",
    //     "--no-first-run",
    //     "--no-sandbox",
    //     "--no-zygote",
    //     "--single-process",
    //   ],
    // }),
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {
  /*  constructor(@InjectBrowser() private readonly browser: Browser) {
    browser.newPage().then(async page => {
      await page.goto(`${config.carUrl}?authKey=1`);
      await page.waitForNetworkIdle({ timeout: 5000 });
      await page.close();
    });
  } */
}
