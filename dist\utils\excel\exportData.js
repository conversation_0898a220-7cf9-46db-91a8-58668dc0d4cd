"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDefaultHeaderCellStyle = getDefaultHeaderCellStyle;
exports.getDefaultCellStyle = getDefaultCellStyle;
exports.countChineseCharacters = countChineseCharacters;
exports.getDefaultBorder = getDefaultBorder;
const exceljs_1 = __importDefault(require("exceljs"));
function getDefaultHeaderCellStyle() {
    return {
        font: {
            size: 11,
            family: 1,
        },
        fill: {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: "FFB8CDE4" },
        },
        alignment: {
            horizontal: "center",
            vertical: "middle",
        },
    };
}
function getDefaultCellStyle() {
    return {
        font: {
            size: 11,
            family: 1,
        },
        alignment: {
            horizontal: "center",
            vertical: "middle",
        },
    };
}
function countChineseCharacters(str) {
    const matches = str.match(/[\u4e00-\u9fa5]/g);
    return matches ? matches.length : 0;
}
function getDefaultBorder() {
    return {
        top: { style: "thin", color: { argb: "FF000000" } },
        left: { style: "thin", color: { argb: "FF000000" } },
        bottom: { style: "thin", color: { argb: "FF000000" } },
        right: { style: "thin", color: { argb: "FF000000" } },
    };
}
class Exporter {
    _workbook;
    constructor(props = {}) {
        this._workbook = new exceljs_1.default.Workbook();
        const { creator, lastModifiedBy, created, modified } = props;
        if (creator) {
            this.workbook.creator = creator;
        }
        if (lastModifiedBy) {
            this.workbook.lastModifiedBy = lastModifiedBy;
        }
        if (created) {
            this.workbook.created = created;
        }
        if (modified) {
            this.workbook.modified = modified;
        }
    }
    addSheet(options) {
        const { name: sheetName = "Sheet1", header, data = [], setCellStyle, autoFit = true, } = options;
        const sheet = this.workbook.addWorksheet(sheetName);
        if (header) {
            sheet.columns = header;
            sheet.eachRow(row => {
                row.eachCell(cell => {
                    cell.style = {
                        ...getDefaultHeaderCellStyle(),
                        border: getDefaultBorder(),
                    };
                });
            });
        }
        const setCell = (cell, colNum, rowNum) => {
            cell.style = getDefaultCellStyle();
            if (setCellStyle) {
                setCellStyle(cell, rowNum, colNum);
            }
        };
        for (const datum of data) {
            const row = sheet.addRow(datum);
            row.eachCell((cell, colNumber) => {
                setCell(cell, colNumber, row.number);
            });
        }
        if (autoFit) {
            (sheet.columns ?? []).forEach(column => {
                let maxWidth = 12;
                for (const value of column.values) {
                    if (value !== null && typeof value !== "undefined") {
                        const values = value.toString().split("\n");
                        for (const val of values) {
                            const chCount = countChineseCharacters(val);
                            maxWidth = Math.max(chCount * 2 + val.length - chCount, maxWidth);
                        }
                    }
                }
                column.width = maxWidth;
            });
        }
        return sheet;
    }
    async doExport(target) {
        if (typeof target === "string") {
            this.workbook.xlsx.writeFile(target);
        }
        else {
            const { filename, response } = target;
            response.contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", `attachment; filename=${encodeURIComponent(filename)}`);
            return this.workbook.xlsx.write(response).then(function () {
                response.status(200).end();
            });
        }
    }
    get workbook() {
        return this._workbook;
    }
}
exports.default = Exporter;
//# sourceMappingURL=exportData.js.map