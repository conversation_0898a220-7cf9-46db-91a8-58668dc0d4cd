"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const properties_1 = require("./utils/properties");
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const transform_interceptor_1 = require("./framework/interceptors/transform/transform.interceptor");
const http_exception_filter_1 = require("./framework/filters/http-exception/http-exception.filter");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.setGlobalPrefix(properties_1.config.contextPath);
    const adapterHost = app.get(core_1.HttpAdapterHost);
    app.useGlobalFilters(new http_exception_filter_1.HttpExceptionFilter(adapterHost));
    app.useGlobalInterceptors(new transform_interceptor_1.TransformInterceptor());
    await app.listen(properties_1.config.port);
    if (module.hot) {
        module.hot.accept();
        module.hot.dispose(() => app.close());
    }
}
bootstrap();
//# sourceMappingURL=main.js.map