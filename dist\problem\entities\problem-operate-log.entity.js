"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProblemOperateLog = void 0;
const base_1 = require("../../framework/model/base");
const typeorm_1 = require("typeorm");
const problem_entity_1 = require("./problem.entity");
const reason_entity_1 = require("./reason.entity");
let ProblemOperateLog = class ProblemOperateLog extends base_1.NumberIdTimeObject {
    problem;
    reason;
    operatorId;
    operatorName;
    action;
    before;
    after;
};
exports.ProblemOperateLog = ProblemOperateLog;
__decorate([
    (0, typeorm_1.ManyToOne)(() => problem_entity_1.Problem, problem => problem.logs, { onDelete: "CASCADE" }),
    __metadata("design:type", Object)
], ProblemOperateLog.prototype, "problem", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => reason_entity_1.Reason, { nullable: true }),
    __metadata("design:type", Object)
], ProblemOperateLog.prototype, "reason", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: "操作人ID" }),
    __metadata("design:type", Number)
], ProblemOperateLog.prototype, "operatorId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: "操作人姓名" }),
    __metadata("design:type", String)
], ProblemOperateLog.prototype, "operatorName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "json", comment: "操作" }),
    __metadata("design:type", Object)
], ProblemOperateLog.prototype, "action", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: "json" }),
    __metadata("design:type", Object)
], ProblemOperateLog.prototype, "before", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: "json" }),
    __metadata("design:type", Object)
], ProblemOperateLog.prototype, "after", void 0);
exports.ProblemOperateLog = ProblemOperateLog = __decorate([
    (0, typeorm_1.Entity)()
], ProblemOperateLog);
//# sourceMappingURL=problem-operate-log.entity.js.map