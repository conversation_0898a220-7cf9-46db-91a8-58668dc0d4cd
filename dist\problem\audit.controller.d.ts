import { Payload } from "src/auth/jwt.strategy";
import { QueryRequest } from "src/framework/service/Query";
import { UserService } from "src/user/user.service";
import { MailService } from "./../mail/mail.service";
import { Reason } from "./entities/reason.entity";
import { ProblemOperateLogService } from "./problem-operate-log.service";
import { ProblemService } from "./problem.service";
import { ReasonService } from "./reason.service";
export declare class AuditController {
    private readonly service;
    private readonly logService;
    private readonly reasonService;
    private readonly mailService;
    private readonly userService;
    constructor(service: ProblemService, logService: ProblemOperateLogService, reasonService: ReasonService, mailService: MailService, userService: UserService);
    query(req: {
        payload: Payload;
    }, request: QueryRequest): Promise<import("../framework/service/IBase.service").Page<import("./entities/problem.entity").Problem> | import("./entities/problem.entity").Problem[]>;
    hasNoPermission(reason: Reason, currentUserId: number): boolean;
    get(id: number): Promise<import("./entities/problem.entity").Problem>;
    submit(req: {
        payload: Payload;
    }, id: number, request: {
        approved: boolean;
        remark?: string;
    }): Promise<void>;
    reasons(req: {
        payload: Payload;
    }, id: number): Promise<Reason[]>;
}
