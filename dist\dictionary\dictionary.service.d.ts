import { QueryRequest } from "src/framework/service/Query";
import { DataSource, Repository } from "typeorm";
import { BaseService } from "../framework/service/base.service";
import { Dictionary, DictionaryCategory } from "./entities/dictionary.entity";
import { DictionaryOption } from "./types";
export declare class DictionaryService extends BaseService<Dictionary> {
    constructor(repository: Repository<Dictionary>, dataSource: DataSource);
    processUpdate(source: Partial<Dictionary>, target: Dictionary): void;
    query(request: QueryRequest): Promise<import("../framework/service/IBase.service").Page<Dictionary> | Dictionary[]>;
    init(): Promise<void>;
    getOptions(categories?: DictionaryCategory[]): Promise<Record<DictionaryCategory, DictionaryOption[]>>;
    getCategoryOptions(category: DictionaryCategory): Promise<DictionaryOption[]>;
    getDictionaryByCategory(category: DictionaryCategory): Promise<Dictionary>;
}
