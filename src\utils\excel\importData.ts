import ExcelJS from "exceljs";
import type { Response } from "express";
export type Validator = (
  row: ExcelJS.Row,
  cell: ExcelJS.Cell,
  key: string,
) => string;
export class SheetNotExistsError extends Error {}

export class ExcelEntityPopulator {
  private workbook: ExcelJS.Workbook;
  private _sheets: { [sheet: string]: ExcelJS.Worksheet };
  private file: string | Buffer;
  private errors: { cell: ExcelJS.Cell; msg: string }[];
  constructor(file: string | Buffer) {
    this.workbook = new ExcelJS.Workbook();
    this.file = file;
    this.errors = [];
  }

  async init() {
    this._sheets = {};
    if (typeof this.file === "string") {
      this.workbook = await this.workbook.xlsx.readFile(this.file);
    } else {
      this.workbook = await this.workbook.xlsx.load(this.file as any);
    }
    this.workbook.worksheets.forEach(
      sheet => (this._sheets[sheet.name] = sheet),
    );
  }

  get sheetNames() {
    return Object.keys(this._sheets);
  }
  get sheets() {
    return Object.values(this._sheets);
  }

  get hasError() {
    return this.errors.length > 0;
  }

  doImport<T>(
    sheetNameOrIndex: string | number,
    header: Record<string, string> = {},
    validators: Validator[] = [],
  ) {
    let sheet: ExcelJS.Worksheet;
    if (typeof sheetNameOrIndex === "string") {
      sheet = this._sheets[sheetNameOrIndex as string];
    } else {
      sheet = this.sheets[sheetNameOrIndex as number];
    }
    if (sheet) {
      const headers: string[] = [];
      const rows: T[] = [];
      sheet.eachRow({ includeEmpty: true }, (row, idx) => {
        if (idx === 1) {
          row.eachCell({ includeEmpty: true }, (cell, col) => {
            if (
              cell.value !== null &&
              !!header[cell.value.toString()]?.length
            ) {
              headers[col] = header[cell.value.toString()];
            }
          });
        } else {
          const item = {};
          for (const idx of Object.keys(headers)) {
            const cell = row.getCell(parseInt(idx, 10));
            const key = headers[idx];
            item[key] = cell.value;
            for (const validator of validators) {
              const msg = validator(row, cell, key);
              if (!!msg?.length) {
                cell.note = msg;
                cell.style = {
                  fill: {
                    type: "pattern",
                    pattern: "solid",
                    fgColor: { argb: "FFFF4D4F" },
                  },
                };
                this.errors.push({ cell, msg });
              }
            }
          }
          if (!!Object.keys(item).length) {
            rows.push(item as T);
          }
        }
      });
      return rows;
    }
    throw new SheetNotExistsError("sheet not exists");
  }

  outputErrors(filename: string, response: Response) {
    response.contentType(
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    );
    response.setHeader(
      "Content-Disposition",
      `attachment; filename=${encodeURIComponent(filename)}`,
    );
    return this.workbook.xlsx.write(response).then(function () {
      response.status(200).end();
    });
  }
}
