"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditController = void 0;
const common_1 = require("@nestjs/common");
const user_service_1 = require("../user/user.service");
const mail_service_1 = require("./../mail/mail.service");
const constant_1 = require("./entities/constant");
const reason_config_entity_1 = require("./entities/reason-config.entity");
const problem_operate_log_service_1 = require("./problem-operate-log.service");
const problem_service_1 = require("./problem.service");
const reason_service_1 = require("./reason.service");
let AuditController = class AuditController {
    service;
    logService;
    reasonService;
    mailService;
    userService;
    constructor(service, logService, reasonService, mailService, userService) {
        this.service = service;
        this.logService = logService;
        this.reasonService = reasonService;
        this.mailService = mailService;
        this.userService = userService;
    }
    async query(req, request) {
        let status;
        let node;
        if (!!request.params?.processStatus?.length) {
            status = request.params.processStatus[0];
            request.params.processStatus = "";
        }
        if (Array.isArray(request.params?.node) &&
            request.params?.node?.length > 0) {
            node = [...request.params.node];
            request.params.node = [];
        }
        const { builder, pageable } = this.service.transfer(request);
        const subQuery = builder
            .subQuery()
            .select("1")
            .from(reason_config_entity_1.ReasonConfig, "prc")
            .innerJoin("prc.reason", "pr")
            .where("prc.state in (:...states) and prc.ownerId = :ownerId", {
            ownerId: req.payload.id,
            states: [
                constant_1.NodeState.ANALYZE_AUDIT,
                constant_1.NodeState.VALIDATE_AUDIT,
                constant_1.NodeState.CQE_AUDIT,
            ],
        })
            .andWhere("pr.delete is false")
            .andWhere(`pr.problem.id = ${builder.alias}.id`)
            .andWhere("pr.stateIdx = prc.stateIdx && pr.status != :status", {
            status: constant_1.ReasonStatus.REJECTED,
        });
        if (!!node?.length) {
            subQuery.andWhere("pr.state in (:...node)", { node });
        }
        builder.andWhereExists(subQuery);
        builder.andWhere(`${builder.alias}.status not in (:...statuses)`, {
            statuses: [
                constant_1.ProblemStatus.DRAFT,
                constant_1.ProblemStatus.CQE,
                constant_1.ProblemStatus.OBSOLETE,
                constant_1.ProblemStatus.CLOSED,
            ],
        });
        builder.addOrderBy(`${builder.alias}.id`, "DESC");
        return await this.service.doQuery(builder, pageable);
    }
    hasNoPermission(reason, currentUserId) {
        const config = reason.configs.find(config => config.state === reason.state);
        return !config || config.ownerId !== currentUserId;
    }
    async get(id) {
        return this.service.get(id);
    }
    async submit(req, id, request) {
        const reason = await this.reasonService.get(id, {
            problem: true,
            configs: true,
        });
        if ((reason.state !== constant_1.NodeState.ANALYZE_AUDIT &&
            reason.state !== constant_1.NodeState.VALIDATE_AUDIT &&
            reason.state !== constant_1.NodeState.CQE_AUDIT) ||
            this.hasNoPermission(reason, req.payload.id)) {
            throw new common_1.ForbiddenException("当前用户不能操作执行该操作");
        }
        let log;
        if (!request.approved) {
            log = this.logService.repository.create({
                problem: { id: reason.problem.id },
                operatorId: req.payload.id,
                operatorName: req.payload.name,
                action: reason.state === constant_1.NodeState.ANALYZE_AUDIT
                    ? {
                        key: "problem.action.reject_analyze",
                        message: `驳回了原因分析和行动计划`,
                        params: { reason: request.remark },
                    }
                    : reason.state === constant_1.NodeState.VALIDATE_AUDIT
                        ? {
                            key: "problem.action.reject_validate",
                            message: `驳回了效果验证`,
                            params: { reason: request.remark },
                        }
                        : {
                            key: "problem.action.reject_cqe",
                            message: `驳回了最终审核`,
                            params: { reason: request.remark },
                        },
            });
            reason.state = constant_1.NodeState.ANALYZE;
            reason.stateIdx = 0;
            reason.status = constant_1.ReasonStatus.REJECTED;
            reason.remark = request.remark;
        }
        else {
            const node = constant_1.nodes.find(node => node.state === reason.state);
            if (!node.next) {
                throw new common_1.ForbiddenException("非法的流程节点");
            }
            log = this.logService.repository.create({
                problem: { id: reason.problem.id },
                operatorId: req.payload.id,
                operatorName: req.payload.name,
                action: reason.state === constant_1.NodeState.ANALYZE_AUDIT
                    ? {
                        key: "problem.action.approve_analyze",
                        message: `批准了原因分析和行动计划`,
                        params: { reason: request.remark },
                    }
                    : reason.state === constant_1.NodeState.VALIDATE_AUDIT
                        ? {
                            key: "problem.action.approve_validate",
                            message: `批准了效果验证`,
                            params: { reason: request.remark },
                        }
                        : {
                            key: "problem.action.approve_cqe",
                            message: `批准了最终审核`,
                            params: { reason: request.remark },
                        },
            });
            reason.remark = null;
            reason.state = node.next;
            reason.stateIdx = constant_1.nodes.find(item => item.state === node.next).index;
            if (reason.state === constant_1.NodeState.COMPLETE) {
                reason.status = constant_1.ReasonStatus.CLOSED;
                reason.finishOn = new Date();
            }
        }
        const result = await this.reasonService.execute(manager => {
            manager.save(this.logService.target, log);
            return manager.save(this.reasonService.target, reason);
        });
        const problem = await this.service.get(reason.problem.id);
        if (await this.reasonService.canFinish(result)) {
            await this.service.execute(manager => {
                problem.status = constant_1.ProblemStatus.CLOSED;
                return manager.save(this.service.target, problem);
            });
            const reasons = await this.reasonService.findByProblemId(reason.problem.id, ["configs", "problem"]);
            if (!!result.todoId) {
                await this.userService.processedTodo(req.payload.token, [
                    result.todoId,
                ]);
            }
            this.mailService.sendEmail(reasons, constant_1.NodeState.COMPLETE, problem);
        }
        else {
            if (!!result.todoId) {
                await this.userService.processedTodo(req.payload.token, [result.todoId], request.approved ? "1" : "2");
            }
            if (reason.state !== constant_1.NodeState.COMPLETE) {
                const results = await this.userService.addTodo(req.payload.token, [result], problem, result.state);
                if (results?.length) {
                    await this.reasonService.execute(async (manager) => {
                        return manager.save(this.reasonService.target, results);
                    });
                }
            }
            this.mailService.sendEmail([result], result.state, problem);
        }
    }
    async reasons(req, id) {
        const reasons = await this.reasonService.findByProblemId(id);
        return reasons.filter(item => !item.delete &&
            item.configs.some(config => config.ownerId === req.payload.id &&
                (config.state === constant_1.NodeState.ANALYZE_AUDIT ||
                    config.state === constant_1.NodeState.VALIDATE_AUDIT ||
                    config.state === constant_1.NodeState.CQE_AUDIT)));
    }
};
exports.AuditController = AuditController;
__decorate([
    (0, common_1.Post)("list"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AuditController.prototype, "query", null);
__decorate([
    (0, common_1.Get)(":id"),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AuditController.prototype, "get", null);
__decorate([
    (0, common_1.Post)("submit/:id"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)("id")),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Object]),
    __metadata("design:returntype", Promise)
], AuditController.prototype, "submit", null);
__decorate([
    (0, common_1.Get)(":id/reason"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], AuditController.prototype, "reasons", null);
exports.AuditController = AuditController = __decorate([
    (0, common_1.Controller)("audit"),
    __metadata("design:paramtypes", [problem_service_1.ProblemService,
        problem_operate_log_service_1.ProblemOperateLogService,
        reason_service_1.ReasonService,
        mail_service_1.MailService,
        user_service_1.UserService])
], AuditController);
//# sourceMappingURL=audit.controller.js.map