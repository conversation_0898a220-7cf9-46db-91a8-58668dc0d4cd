{"version": 3, "file": "HeaderTransformer.js", "sourceRoot": "", "sources": ["../../../src/transforms/HeaderTransformer.ts"], "names": [], "mappings": ";;;;;;AAAA,4EAA6C;AAC7C,0EAA2C;AAC3C,8DAA+B;AAC/B,oEAAqC;AAYrC,MAAa,iBAAiB;IAe1B,YAAmB,aAA4B;QAZ/C,YAAO,GAAuB,IAAI,CAAC;QAE3B,oBAAe,GAAG,KAAK,CAAC;QAEf,sBAAiB,GAAY,KAAK,CAAC;QAE5C,sBAAiB,GAAG,KAAK,CAAC;QAE1B,kBAAa,GAAG,CAAC,CAAC;QAKtB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,aAAa,CAAC,OAAO,KAAK,IAAI,EAAE;YAChC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SACjC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE;YAC7C,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;SAC1C;aAAM,IAAI,2BAAU,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE;YAC1C,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,OAAO,CAAC;SACjD;IACL,CAAC;IAEM,SAAS,CAAC,GAAa,EAAE,EAA2B;QACvD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE;YACzB,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;SACjD;QACD,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1C,CAAC;IAEO,YAAY,CAAC,GAAQ;QACzB,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,aAAa,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAClF,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACvB,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;aACvF;YACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC9B,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC7C,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;aAC/C;iBAAM,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC/B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;aACxB;iBAAM;gBACH,mGAAmG;gBACnG,OAAO,IAAI,CAAC;aACf;YACD,OAAO,KAAK,CAAC;SAChB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,UAAU,CAAC,GAAqB;QACpC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO,EAAE,GAAG,EAAG,GAAkB,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;SACtD;QACD,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,aAAa,CAAC,sBAAsB,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE;YAC1E,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE;gBACrC,MAAM,IAAI,KAAK,CACX,sDAAsD,IAAI,CAAC,aAAa,iBAAiB,GAAG,CAAC,MAAM,EAAE,CACxG,CAAC;aACL;YACD,OAAO;gBACH,GAAG,EAAG,GAAkB;gBACxB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,oCAAoC,IAAI,CAAC,aAAa,iBAAiB,GAAG,CAAC,MAAM,EAAE;aAC9F,CAAC;SACL;QACD,IAAI,aAAa,CAAC,oBAAoB,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE;YACvE,OAAO;gBACH,GAAG,EAAG,GAAkB;gBACxB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,oCAAoC,IAAI,CAAC,aAAa,iBAAiB,GAAG,CAAC,MAAM,EAAE;aAC9F,CAAC;SACL;QACD,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IACxD,CAAC;IAEO,UAAU,CAAC,GAAqB;QACpC,MAAM,MAAM,GAAW,EAAE,CAAC;QAC1B,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC;QACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,IAAI,CAAC,EAAE;YACvC,MAAM,MAAM,GAAI,OAAoB,CAAC,CAAC,CAAC,CAAC;YACxC,IAAI,CAAC,4BAAW,CAAC,MAAM,CAAC,EAAE;gBACtB,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;gBACnB,6CAA6C;gBAC7C,IAAI,4BAAW,CAAC,GAAG,CAAC,EAAE;oBAClB,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;iBACvB;qBAAM;oBACH,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;iBACxB;aACJ;SACJ;QACD,OAAO,MAAW,CAAC;IACvB,CAAC;IAEO,UAAU,CAAC,OAAoB;;QACnC,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,IAAI,qBAAI,CAAC,eAAe,CAAC,CAAC,MAAM,KAAK,eAAe,CAAC,MAAM,EAAE;YACzD,MAAM,OAAO,GAAG,wBAAO,CAAC,eAAe,CAAC,CAAC;YACzC,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACjF,MAAM,IAAI,KAAK,CAAC,2BAA2B,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;SAC5E;QACD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,OAAA,IAAI,CAAC,OAAO,0CAAE,MAAM,KAAI,CAAC,CAAC;IACnD,CAAC;CACJ;AAhHD,8CAgHC"}