import type { Response } from "express";
import type { QueryRequest } from "../framework/service/Query";
import { DictionaryRelationService } from "./dictionary-relation.service";
import { DictionaryService } from "./dictionary.service";
import { Dictionary, DictionaryCategory, DictionaryRelation } from "./entities/dictionary.entity";
import { RemoteDictionaryService } from "./remote-dictionary.service";
import { DictionaryOption, DictionarySyncRequest } from "./types";
export declare class DictionaryController {
    private readonly service;
    private readonly rmService;
    private readonly drService;
    constructor(service: DictionaryService, rmService: RemoteDictionaryService, drService: DictionaryRelationService);
    query(request: QueryRequest): Promise<Dictionary[] | import("../framework/service/IBase.service").Page<Dictionary>>;
    save(entity: Partial<Dictionary>): Promise<Dictionary>;
    getOptions(request: {
        categories?: DictionaryCategory[];
    }): Promise<Record<DictionaryCategory, DictionaryOption[]>>;
    getCategoryOptions(category: DictionaryCategory): Promise<{
        name: string;
    }[]>;
    getCategoryTemplate(category: DictionaryCategory, res: Response): Promise<void>;
    importDict(category: DictionaryCategory, file: Express.Multer.File): Promise<void>;
    getMachineTypeRelations(): Promise<DictionaryRelation[] | import("../framework/service/IBase.service").Page<DictionaryRelation>>;
    getMachineTypeRelationTemplate(res: Response): Promise<void>;
    private genKey;
    importRelation(file: Express.Multer.File, res: Response): Promise<void>;
    dictSync(request: DictionarySyncRequest): Promise<void>;
}
