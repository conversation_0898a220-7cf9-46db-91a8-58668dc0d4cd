{"name": "@fastify/cors", "version": "9.0.1", "description": "Fastify CORS", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"coverage": "tap --cov --coverage-report=html test", "lint": "standard", "lint:fix": "standard --fix", "test": "npm run test:unit && npm run test:typescript", "test:typescript": "tsd", "test:unit": "tap test/*.test.js"}, "keywords": ["fastify", "cors", "headers", "access", "control"], "author": "<PERSON> - @delvedor (http://delved.org)", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/fastify/fastify-cors.git"}, "bugs": {"url": "https://github.com/fastify/fastify-cors/issues"}, "homepage": "https://github.com/fastify/fastify-cors#readme", "devDependencies": {"@fastify/pre-commit": "^2.0.2", "@types/node": "^20.1.0", "@typescript-eslint/eslint-plugin": "^6.4.1", "@typescript-eslint/parser": "^6.4.0", "cors": "^2.8.5", "fastify": "^4.0.0-rc.2", "standard": "^17.0.0", "tap": "16.3.9", "tsd": "^0.30.0", "typescript": "^5.0.2"}, "dependencies": {"fastify-plugin": "^4.0.0", "mnemonist": "0.39.6"}, "tsd": {"directory": "test"}, "publishConfig": {"access": "public"}, "pre-commit": ["lint", "test"]}