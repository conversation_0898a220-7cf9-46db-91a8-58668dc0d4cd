import ExcelJS from "exceljs";
import type { Response } from "express";
export type Validator = (row: ExcelJS.Row, cell: ExcelJS.Cell, key: string) => string;
export declare class SheetNotExistsError extends Error {
}
export declare class ExcelEntityPopulator {
    private workbook;
    private _sheets;
    private file;
    private errors;
    constructor(file: string | Buffer);
    init(): Promise<void>;
    get sheetNames(): string[];
    get sheets(): ExcelJS.Worksheet[];
    get hasError(): boolean;
    doImport<T>(sheetNameOrIndex: string | number, header?: Record<string, string>, validators?: Validator[]): T[];
    outputErrors(filename: string, response: Response): Promise<void>;
}
