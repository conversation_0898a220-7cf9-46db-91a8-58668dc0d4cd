"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseRemoteService = void 0;
const node_fetch_1 = __importStar(require("node-fetch"));
const properties_1 = require("../utils/properties");
class BaseRemoteService {
    async execute(api, method, token, body, header) {
        const myHeaders = new node_fetch_1.Headers();
        if (!!token?.length) {
            myHeaders.append("Authorization", `Bearer ${token}`);
        }
        if (!!header) {
            for (const key of Object.keys(header)) {
                myHeaders.append(key, header[key]);
            }
        }
        const option = {
            method,
            headers: myHeaders,
            redirect: "follow",
        };
        if (!!body) {
            myHeaders.append("Content-Type", "application/json");
            option.body = JSON.stringify(body);
        }
        const res = await (0, node_fetch_1.default)(`${properties_1.config.baseApi}${api}`, option);
        const { success, data } = await res.json();
        return success ? data : null;
    }
}
exports.BaseRemoteService = BaseRemoteService;
//# sourceMappingURL=base-remote.service.js.map