import { DynamicModule } from "@nestjs/common";
declare class Config {
    private _entities;
    private _car_url;
    private _jwt_secret;
    private _jwt_expired;
    private _properties;
    private _base_storage_dir;
    private _server_url;
    private _login_page;
    private _context_path;
    private _mail;
    private _test_copyers;
    private _complete_cc;
    private _port;
    private _mail_template;
    private _contextPath;
    private _app_name;
    private loadYaml;
    private loadProperties;
    private getPath;
    private getType;
    constructor();
    get appName(): string;
    get testCopyers(): string[];
    get completeCc(): string[];
    get jwtSecret(): string;
    get jwtExpired(): string | number;
    get properties(): Record<string, any>;
    get carUrl(): string;
    get baseStoreageDir(): string;
    get loginPage(): string;
    get baseApi(): string;
    get baseUrl(): string;
    get mailTemplateDir(): any;
    get port(): number;
    get mailConfig(): Record<string, any>;
    get contextPath(): string;
    getDatasource(): DynamicModule[];
}
export declare const config: Config;
export {};
