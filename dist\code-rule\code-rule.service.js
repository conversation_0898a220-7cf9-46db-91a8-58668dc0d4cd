"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeRuleService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const dayjs_1 = __importDefault(require("dayjs"));
const dictionary_entity_1 = require("../dictionary/entities/dictionary.entity");
const typeorm_2 = require("typeorm");
const base_service_1 = require("../framework/service/base.service");
const code_rule_entity_1 = require("./entities/code-rule.entity");
const properties_1 = require("../utils/properties");
let CodeRuleService = class CodeRuleService extends base_service_1.BaseService {
    constructor(repository, dataSource) {
        super(repository, dataSource);
    }
    mapper(datum) {
        const { id, name } = datum.dictionary;
        return { ...datum, dictionary: { id, name } };
    }
    async genCode(problem, problemRepo) {
        const { machineType, customer, productLine, factory, businessUnit } = problem;
        const codeRules = await this.repository.find();
        const rules = [];
        codeRules.forEach(entity => {
            if (entity.option === machineType &&
                entity.dictionary.category === dictionary_entity_1.DictionaryCategory.MACHINE_TYPE) {
                rules.push(entity);
            }
            else if (entity.option === customer &&
                entity.dictionary.category === dictionary_entity_1.DictionaryCategory.CUSTOMER) {
                rules.push(entity);
            }
            else if (entity.option === productLine &&
                entity.dictionary.category === dictionary_entity_1.DictionaryCategory.PRODUCT_LINE) {
                rules.push(entity);
            }
            else if (entity.option === factory &&
                entity.dictionary.category === dictionary_entity_1.DictionaryCategory.FACTORY) {
                rules.push(entity);
            }
            else if (entity.option === businessUnit &&
                entity.dictionary.category === dictionary_entity_1.DictionaryCategory.BUSINESS_UNIT) {
                rules.push(entity);
            }
        });
        rules.sort((r1, r2) => r1.dictionary.priority - r2.dictionary.priority);
        const prefix = rules.map(rule => rule.code).join("");
        const date = (0, dayjs_1.default)().format("YYYYMMDD");
        const fullPrefix = `${properties_1.config.appName}${prefix}${date}`;
        const maxCodeProblem = await problemRepo.findOne({
            select: ["code"],
            where: { code: (0, typeorm_2.Like)(`${fullPrefix}%`) },
            order: { code: "DESC" },
        });
        const code = !maxCodeProblem
            ? 1
            : parseInt(maxCodeProblem.code.substring(fullPrefix.length), 10) + 1;
        problem.code = `${fullPrefix}${String(code).padStart(3, "0")}`;
    }
    async duplicate(entity) {
        const where = {
            dictionary: { id: entity.dictionary.id },
            option: entity.option,
        };
        if (entity.id) {
            where.id = (0, typeorm_2.Not)(entity.id);
        }
        return await this.repository.exist({ where });
    }
};
exports.CodeRuleService = CodeRuleService;
exports.CodeRuleService = CodeRuleService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(code_rule_entity_1.CodeRule)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource])
], CodeRuleService);
//# sourceMappingURL=code-rule.service.js.map