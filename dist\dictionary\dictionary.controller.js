"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DictionaryController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const multer_1 = require("multer");
const public_decorator_1 = require("../auth/public.decorator");
const exportData_1 = __importDefault(require("../utils/excel/exportData"));
const importData_1 = require("../utils/excel/importData");
const dictionary_relation_service_1 = require("./dictionary-relation.service");
const dictionary_service_1 = require("./dictionary.service");
const dictionary_entity_1 = require("./entities/dictionary.entity");
const remote_dictionary_service_1 = require("./remote-dictionary.service");
let DictionaryController = class DictionaryController {
    service;
    rmService;
    drService;
    constructor(service, rmService, drService) {
        this.service = service;
        this.rmService = rmService;
        this.drService = drService;
    }
    async query(request) {
        const data = await this.service.query(request);
        let datum;
        if (Array.isArray(data)) {
            datum = data.find(item => item.category === dictionary_entity_1.DictionaryCategory.FACTORY);
        }
        else {
            datum = data.data.find(item => item.category === dictionary_entity_1.DictionaryCategory.FACTORY);
        }
        if (!!datum) {
            const factories = await this.rmService.getFactories();
            datum.options = factories.map(factory => ({ name: factory.label }));
        }
        return data;
    }
    async save(entity) {
        return await this.service.saveOrUpdate(entity);
    }
    async getOptions(request) {
        const result = await this.service.getOptions(request.categories);
        if (result.hasOwnProperty(dictionary_entity_1.DictionaryCategory.FACTORY)) {
            const factories = await this.rmService.getFactories();
            result[dictionary_entity_1.DictionaryCategory.FACTORY] = factories.map(factory => ({
                name: factory.label,
            }));
        }
        return result;
    }
    async getCategoryOptions(category) {
        if (category === dictionary_entity_1.DictionaryCategory.FACTORY) {
            const factories = await this.rmService.getFactories();
            return factories.map(factory => ({ name: factory.label }));
        }
        return await this.service.getCategoryOptions(category);
    }
    async getCategoryTemplate(category, res) {
        if (category === dictionary_entity_1.DictionaryCategory.FACTORY) {
            throw new common_1.ForbiddenException("工厂字典不支持导入");
        }
        const dictName = dictionary_entity_1.DictionaryCategoryOpions[category];
        const options = await this.service.getCategoryOptions(category);
        const exporter = new exportData_1.default();
        let data = [];
        let header = [];
        if (category === dictionary_entity_1.DictionaryCategory.REASON_TYPE) {
            for (const option of options) {
                if (!!option.children?.length) {
                    for (const child of option.children) {
                        data.push({ name: option.name, childName: child.name });
                    }
                }
                else {
                    data.push({ name: option.name, childName: "" });
                }
            }
            header = [
                { header: `一级${dictName}`, key: "name" },
                { header: `二级${dictName}`, key: "childName" },
            ];
        }
        else {
            header = [{ header: `${dictName}`, key: "name" }];
            data = options;
        }
        exporter.addSheet({
            name: category,
            header,
            data,
        });
        exporter.doExport({
            filename: dictName + "字典导入模板.xlsx",
            response: res,
        });
    }
    async importDict(category, file) {
        if (category === dictionary_entity_1.DictionaryCategory.FACTORY) {
            throw new common_1.ForbiddenException("工厂字典不支持导入");
        }
        const dictName = dictionary_entity_1.DictionaryCategoryOpions[category];
        const populator = new importData_1.ExcelEntityPopulator(file.buffer);
        await populator.init();
        if (!populator.sheetNames?.length || populator.sheetNames[0] !== category) {
            throw new common_1.ForbiddenException("请使用正确的字典");
        }
        const dictionary = await this.service.getDictionaryByCategory(category);
        if (category === dictionary_entity_1.DictionaryCategory.REASON_TYPE) {
            const options = populator.doImport(0, {
                [`一级${dictName}`]: "name",
                [`二级${dictName}`]: "childName",
            });
            const values = {};
            for (const option of options) {
                if (!!option.name?.length && !!option.childName?.length) {
                    values[option.name] = values[option.name] ?? [];
                    if (!values[option.name].includes(option.childName)) {
                        values[option.name].push(option.childName);
                    }
                }
            }
            dictionary.options = Object.entries(values).map(([name, children]) => {
                return { name, children: children.map(item => ({ name: item })) };
            });
        }
        else {
            const options = populator.doImport(0, {
                [dictName]: "name",
            });
            console.log(options);
            const values = [];
            for (const option of options) {
                if (!!option.name?.length &&
                    !!option.name.trim().length &&
                    !values.includes(option.name)) {
                    values.push(option.name);
                }
            }
            dictionary.options = values.map(name => ({ name }));
        }
        await this.service.saveOrUpdate(dictionary);
    }
    async getMachineTypeRelations() {
        return await this.drService.query({});
    }
    async getMachineTypeRelationTemplate(res) {
        const exporter = new exportData_1.default();
        const customers = await this.service.getCategoryOptions(dictionary_entity_1.DictionaryCategory.CUSTOMER);
        const businessUnits = await this.service.getCategoryOptions(dictionary_entity_1.DictionaryCategory.BUSINESS_UNIT);
        const machineTypes = await this.service.getCategoryOptions(dictionary_entity_1.DictionaryCategory.MACHINE_TYPE);
        const rows = Math.max(machineTypes.length, Math.max(businessUnits.length, customers.length));
        const relations = (await this.drService.query({}));
        const sheet1 = exporter.addSheet({
            name: "Sheet1",
            header: [
                { header: "机型", key: "machineType" },
                { header: "客户", key: "customer" },
                { header: "事业部", key: "businessUnit" },
            ],
            data: relations,
        });
        const { dataValidations } = sheet1;
        dataValidations.add("A2:A5000", {
            type: "list",
            allowBlank: false,
            formulae: [`Sheet2!$A$1:$A$${machineTypes.length}`],
        });
        dataValidations.add("B2:B5000", {
            type: "list",
            allowBlank: false,
            formulae: [`Sheet2!$B$1:$B$${customers.length}`],
        });
        dataValidations.add("C2:C5000", {
            type: "list",
            allowBlank: false,
            formulae: [`Sheet2!$C$1:$C$${businessUnits.length}`],
        });
        const sheet2 = exporter.addSheet({
            name: "Sheet2",
        });
        for (let i = 0; i < rows; i++) {
            const customer = customers.length > i ? customers[i].name : undefined;
            const businessUnit = businessUnits.length > i ? businessUnits[i].name : undefined;
            const machineType = machineTypes.length > i ? machineTypes[i].name : undefined;
            sheet2.addRow([machineType, customer, businessUnit]);
        }
        exporter.doExport({
            filename: "机型客户事业部字典关联维护模板.xlsx",
            response: res,
        });
    }
    genKey(datum) {
        return `${datum.machineType}$$$${datum.customer}$$$${datum.businessUnit}`;
    }
    async importRelation(file, res) {
        const populator = new importData_1.ExcelEntityPopulator(file.buffer);
        await populator.init();
        const sheet = populator.sheets[0];
        if (sheet.getCell("A1")?.value !== "机型" ||
            sheet.getCell("B1")?.value !== "客户" ||
            sheet.getCell("C1")?.value !== "事业部") {
            throw new common_1.ForbiddenException("请使用正确的字典");
        }
        const mtDict = await this.service.getDictionaryByCategory(dictionary_entity_1.DictionaryCategory.MACHINE_TYPE);
        const ctDict = await this.service.getDictionaryByCategory(dictionary_entity_1.DictionaryCategory.CUSTOMER);
        const buDict = await this.service.getDictionaryByCategory(dictionary_entity_1.DictionaryCategory.BUSINESS_UNIT);
        const mtOptions = mtDict.options.map(item => item.name);
        const ctOptions = ctDict.options.map(item => item.name);
        const buOptions = buDict.options.map(item => item.name);
        const rows = populator.doImport(0, {
            机型: "machineType",
            客户: "customer",
            事业部: "businessUnit",
        }, [
            (row, cell, key) => {
                if (key === "machineType") {
                    if (cell.value === null || !cell.value.toString().trim().length) {
                        return "机型不可为空";
                    }
                    if (!mtOptions.includes(cell.value.toString().trim())) {
                        return `机型[${cell.value.toString().trim()}]不在机型字典中`;
                    }
                }
                else if (key === "customer") {
                    if (cell.value === null || !cell.value.toString().trim().length) {
                        return "客户不可为空";
                    }
                    if (!ctOptions.includes(cell.value.toString().trim())) {
                        return `客户[${cell.value.toString().trim()}]不在客户字典中`;
                    }
                }
                else if (key === "businessUnit") {
                    if (cell.value === null || !cell.value.toString().trim().length) {
                        return undefined;
                    }
                    if (!buOptions.includes(cell.value.toString().trim())) {
                        return `事业部[${cell.value.toString().trim()}]不在事业部字典中`;
                    }
                }
                return undefined;
            },
        ]);
        if (populator.hasError) {
            populator.outputErrors(file.originalname, res);
        }
        else {
            const data = (await this.drService.query({}));
            const keyMap = new Map();
            for (const datum of data) {
                keyMap.set(this.genKey(datum), datum.id);
            }
            const rowKeys = rows.map(row => this.genKey(row));
            const toRemoveIds = [];
            for (const [key, id] of keyMap.entries()) {
                if (!rowKeys.includes(key)) {
                    toRemoveIds.push(id);
                    keyMap.delete(key);
                }
            }
            const toSaveEntities = [];
            for (const row of rows) {
                const key = this.genKey(row);
                if (!keyMap.has(key)) {
                    const entity = new dictionary_entity_1.DictionaryRelation();
                    entity.customer = row.customer;
                    if (!!row.businessUnit?.length) {
                        entity.businessUnit = row.businessUnit;
                    }
                    entity.machineType = row.machineType;
                    toSaveEntities.push(entity);
                }
            }
            await this.drService.execute(async (manager) => {
                if (toRemoveIds.length) {
                    await manager
                        .createQueryBuilder()
                        .delete()
                        .from(this.drService.target, "entity")
                        .whereInIds(toRemoveIds)
                        .execute();
                }
                if (toSaveEntities.length) {
                    await manager
                        .createQueryBuilder()
                        .insert()
                        .into(this.drService.target)
                        .values(toSaveEntities)
                        .execute();
                }
                return new Promise(resolve => {
                    resolve(true);
                });
            });
            res.json({ code: 0 }).status(common_1.HttpStatus.OK);
        }
    }
    async dictSync(request) {
        const data = (await this.drService.query({}));
        const keyMap = new Map();
        for (const datum of data) {
            keyMap.set(this.genKey(datum), datum.id);
        }
        const rowKeys = request.relations.map(row => this.genKey(row));
        const toRemoveIds = [];
        for (const [key, id] of keyMap.entries()) {
            if (!rowKeys.includes(key)) {
                toRemoveIds.push(id);
                keyMap.delete(key);
            }
        }
        const toSaveEntities = [];
        for (const row of request.relations) {
            const key = this.genKey(row);
            if (!keyMap.has(key)) {
                const entity = new dictionary_entity_1.DictionaryRelation();
                entity.customer = row.customer;
                entity.businessUnit = row.businessUnit;
                entity.machineType = row.machineType;
                toSaveEntities.push(entity);
            }
        }
        await this.drService.execute(async (manager) => {
            if (request.machineTypes?.length) {
                await manager
                    .createQueryBuilder()
                    .update(this.service.target)
                    .set({ options: request.machineTypes.forEach(name => ({ name })) })
                    .where("category = :category", {
                    category: dictionary_entity_1.DictionaryCategory.MACHINE_TYPE,
                })
                    .execute();
            }
            if (request.customers?.length) {
                await manager
                    .createQueryBuilder()
                    .update(this.service.target)
                    .set({ options: request.customers.forEach(name => ({ name })) })
                    .where("category = :category", {
                    category: dictionary_entity_1.DictionaryCategory.CUSTOMER,
                })
                    .execute();
            }
            if (request.businessUnits?.length) {
                await manager
                    .createQueryBuilder()
                    .update(this.service.target)
                    .set({ options: request.businessUnits.forEach(name => ({ name })) })
                    .where("category = :category", {
                    category: dictionary_entity_1.DictionaryCategory.BUSINESS_UNIT,
                })
                    .execute();
            }
            if (toRemoveIds.length) {
                await manager
                    .createQueryBuilder()
                    .delete()
                    .from(this.drService.target, "entity")
                    .whereInIds(toRemoveIds)
                    .execute();
            }
            if (toSaveEntities.length) {
                await manager
                    .createQueryBuilder()
                    .insert()
                    .into(this.drService.target)
                    .values(toSaveEntities)
                    .execute();
            }
            return new Promise(resolve => {
                resolve(true);
            });
        });
    }
};
exports.DictionaryController = DictionaryController;
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)("list"),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DictionaryController.prototype, "query", null);
__decorate([
    (0, common_1.Post)("/"),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DictionaryController.prototype, "save", null);
__decorate([
    (0, common_1.Post)("options"),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DictionaryController.prototype, "getOptions", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)(":category/options"),
    __param(0, (0, common_1.Param)("category")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DictionaryController.prototype, "getCategoryOptions", null);
__decorate([
    (0, common_1.Get)(":category/template"),
    __param(0, (0, common_1.Param)("category")),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DictionaryController.prototype, "getCategoryTemplate", null);
__decorate([
    (0, common_1.Post)(":category/import"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("file", {
        storage: (0, multer_1.memoryStorage)(),
    })),
    __param(0, (0, common_1.Param)("category")),
    __param(1, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DictionaryController.prototype, "importDict", null);
__decorate([
    (0, common_1.Get)("relations/list"),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DictionaryController.prototype, "getMachineTypeRelations", null);
__decorate([
    (0, common_1.Get)("relations"),
    __param(0, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DictionaryController.prototype, "getMachineTypeRelationTemplate", null);
__decorate([
    (0, common_1.Post)("relations"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("file", {
        storage: (0, multer_1.memoryStorage)(),
    })),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], DictionaryController.prototype, "importRelation", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)("sync"),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DictionaryController.prototype, "dictSync", null);
exports.DictionaryController = DictionaryController = __decorate([
    (0, common_1.Controller)("dictionary"),
    __metadata("design:paramtypes", [dictionary_service_1.DictionaryService,
        remote_dictionary_service_1.RemoteDictionaryService,
        dictionary_relation_service_1.DictionaryRelationService])
], DictionaryController);
//# sourceMappingURL=dictionary.controller.js.map