"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardController = void 0;
const common_1 = require("@nestjs/common");
const dayjs_1 = __importDefault(require("dayjs"));
const public_decorator_1 = require("../auth/public.decorator");
const reason_config_entity_1 = require("../problem/entities/reason-config.entity");
const typeorm_1 = require("typeorm");
const constant_1 = require("./../problem/entities/constant");
const problem_service_1 = require("./../problem/problem.service");
const reason_service_1 = require("./../problem/reason.service");
const dashboard_service_1 = require("./dashboard.service");
let DashboardController = class DashboardController {
    dashboardService;
    problemService;
    reasonService;
    constructor(dashboardService, problemService, reasonService) {
        this.dashboardService = dashboardService;
        this.problemService = problemService;
        this.reasonService = reasonService;
    }
    async panel() {
        const [open, follow] = await Promise.all([
            this.problemService.repository.countBy({
                status: (0, typeorm_1.Equal)(constant_1.ProblemStatus.NEW),
            }),
            this.problemService.repository.countBy({
                status: (0, typeorm_1.Equal)(constant_1.ProblemStatus.PROCESSING),
            }),
        ]);
        return {
            open,
            follow,
        };
    }
    async processing(req) {
        const builder = this.problemService.repository
            .createQueryBuilder()
            .where("status not in (:...statuses)", {
            statuses: [
                constant_1.ProblemStatus.OBSOLETE,
                constant_1.ProblemStatus.DRAFT,
                constant_1.ProblemStatus.CQE,
                constant_1.ProblemStatus.CLOSED,
            ],
        });
        const subQuery = builder
            .subQuery()
            .select("1")
            .from(reason_config_entity_1.ReasonConfig, "prc")
            .innerJoin("prc.reason", "pr")
            .where("prc.state in (:...states) and prc.ownerId = :ownerId", {
            ownerId: req.payload.id,
            states: [
                constant_1.NodeState.ANALYZE,
                constant_1.NodeState.VALIDATE,
                constant_1.NodeState.ANALYZE_AUDIT,
                constant_1.NodeState.VALIDATE_AUDIT,
                constant_1.NodeState.CQE_AUDIT,
            ],
        })
            .andWhere("pr.delete is false")
            .andWhere(`pr.problem.id = ${builder.alias}.id`)
            .andWhere("prc.stateIdx = pr.stateIdx");
        builder.andWhereExists(subQuery);
        return builder.getCount();
    }
    async getTaskCount(req) {
        const { builder } = this.problemService.transfer({});
        const subQuery = builder
            .subQuery()
            .select("1")
            .from(reason_config_entity_1.ReasonConfig, "prc")
            .innerJoin("prc.reason", "pr")
            .where("prc.state in (:...states) and prc.ownerId = :ownerId", {
            ownerId: req.payload.id,
            states: [constant_1.NodeState.ANALYZE, constant_1.NodeState.VALIDATE],
        })
            .andWhere("pr.delete is false")
            .andWhere(`pr.problem.id = ${builder.alias}.id`)
            .andWhere("prc.stateIdx = pr.stateIdx");
        builder.andWhereExists(subQuery);
        builder.andWhere(`${builder.alias}.status not in (:...statuses)`, {
            statuses: [
                constant_1.ProblemStatus.DRAFT,
                constant_1.ProblemStatus.CQE,
                constant_1.ProblemStatus.OBSOLETE,
                constant_1.ProblemStatus.CLOSED,
            ],
        });
        return await builder.getCount();
    }
    async getAuditingCount(req) {
        const { builder } = this.problemService.transfer({});
        const subQuery = builder
            .subQuery()
            .select("1")
            .from(reason_config_entity_1.ReasonConfig, "prc")
            .innerJoin("prc.reason", "pr")
            .where("prc.state in (:...states) and prc.ownerId = :ownerId", {
            ownerId: req.payload.id,
            states: [
                constant_1.NodeState.ANALYZE_AUDIT,
                constant_1.NodeState.VALIDATE_AUDIT,
                constant_1.NodeState.CQE_AUDIT,
            ],
        })
            .andWhere("pr.delete is false")
            .andWhere(`pr.problem.id = ${builder.alias}.id`)
            .andWhere("pr.stateIdx = prc.stateIdx && pr.status != :status", {
            status: constant_1.ReasonStatus.REJECTED,
        });
        builder.andWhereExists(subQuery);
        builder.andWhere(`${builder.alias}.status not in (:...statuses)`, {
            statuses: [
                constant_1.ProblemStatus.DRAFT,
                constant_1.ProblemStatus.CQE,
                constant_1.ProblemStatus.OBSOLETE,
                constant_1.ProblemStatus.CLOSED,
            ],
        });
        return await builder.getCount();
    }
    async todo(req) {
        return {
            task: await this.getTaskCount(req),
            audit: await this.getAuditingCount(req),
        };
    }
    async panelData(status) {
        const builder = this.problemService.repository.createQueryBuilder();
        builder.where(`${builder.alias}.status = :status`, {
            status,
        });
        return this.problemService.doQuery(builder, false);
    }
    async processingData(req) {
        const builder = this.problemService.repository.createQueryBuilder();
        builder.where(`${builder.alias}.status not in (:...statuses)`, {
            statuses: [
                constant_1.ProblemStatus.OBSOLETE,
                constant_1.ProblemStatus.DRAFT,
                constant_1.ProblemStatus.CQE,
                constant_1.ProblemStatus.CLOSED,
            ],
        });
        const subQuery = builder
            .subQuery()
            .select("1")
            .from(reason_config_entity_1.ReasonConfig, "prc")
            .innerJoin("prc.reason", "pr")
            .where("prc.state in (:...states) and prc.ownerId = :ownerId", {
            ownerId: req.payload.id,
            states: [
                constant_1.NodeState.ANALYZE,
                constant_1.NodeState.VALIDATE,
                constant_1.NodeState.ANALYZE_AUDIT,
                constant_1.NodeState.VALIDATE_AUDIT,
                constant_1.NodeState.CQE_AUDIT,
            ],
        })
            .andWhere("pr.delete is false")
            .andWhere(`pr.problem.id = ${builder.alias}.id`)
            .andWhere("prc.stateIdx = pr.stateIdx");
        builder.andWhereExists(subQuery);
        return this.problemService.doQuery(builder, false);
    }
    getBaseBuilder(request, skip = false) {
        let monthRange;
        let unqualityType;
        let category;
        let subCategory;
        if (Array.isArray(request.params?.monthRange) &&
            request.params?.monthRange?.length > 0) {
            monthRange = [...request.params.monthRange];
            request.params.monthRange = [];
        }
        if (Array.isArray(request.params?.unqualityType) &&
            request.params?.unqualityType?.length > 0) {
            unqualityType = request.params.unqualityType[0];
            request.params.unqualityType = [];
        }
        if (Array.isArray(request.params?.category) &&
            request.params?.category?.length > 0) {
            category = request.params.category[0];
            request.params.category = [];
        }
        if (Array.isArray(request.params?.subCategory) &&
            request.params?.subCategory?.length > 0) {
            subCategory = request.params.subCategory[0];
            request.params.subCategory = [];
        }
        const { builder } = this.problemService.transfer(request);
        builder.andWhere(`${builder.alias}.status in (:...statuses)`, {
            statuses: [
                constant_1.ProblemStatus.NEW,
                constant_1.ProblemStatus.PROCESSING,
                constant_1.ProblemStatus.CLOSED,
            ],
        });
        if (!!monthRange?.length) {
            builder.andWhere(`${builder.alias}.createdOn >= :from and ${builder.alias}.createdOn <= :to`, {
                from: (0, dayjs_1.default)(`${monthRange[0]}`).startOf("M").toDate(),
                to: (0, dayjs_1.default)(`${monthRange[1]}`).endOf("M").toDate(),
            });
        }
        if (!!unqualityType?.length) {
            if (!skip) {
                builder.leftJoin(`${builder.alias}.reasons`, "r");
            }
            builder
                .andWhere("r.delete is false")
                .andWhere("r.unqualityType = :unqualityType", { unqualityType });
        }
        if (!!subCategory?.length && !!category?.length) {
            if (!skip) {
                builder.leftJoin(`${builder.alias}.reasons`, "r");
            }
            builder
                .andWhere("r.delete is false")
                .andWhere("r.subCategory = :subCategory", { subCategory })
                .andWhere("r.category = :category", { category });
        }
        return builder;
    }
    async stepChart(pie, request) {
        const builder = this.getBaseBuilder(request);
        const total = pie === "1" ? await builder.getCount() : 0;
        builder
            .select(`${builder.alias}.productStep`, "type")
            .addSelect("count(1)", "value")
            .groupBy(`${builder.alias}.productStep`);
        if (pie === "0") {
            builder.addSelect(`DATE_FORMAT(${builder.alias}.created_on,'%Y-%m')`, "month");
            builder.addGroupBy(`DATE_FORMAT(${builder.alias}.created_on,'%Y-%m')`);
        }
        const data = await builder.getRawMany();
        return { data, total };
    }
    async customerChart(pie, request) {
        const builder = this.getBaseBuilder(request);
        const total = pie === "1" ? await builder.getCount() : 0;
        builder
            .select(`${builder.alias}.customer`, "type")
            .addSelect("count(1)", "value")
            .groupBy(`${builder.alias}.customer`);
        if (pie === "0") {
            builder.addSelect(`DATE_FORMAT(${builder.alias}.createdOn,'%Y-%m')`, "month");
            builder.addGroupBy(`DATE_FORMAT(${builder.alias}.createdOn,'%Y-%m')`);
        }
        const data = await builder.getRawMany();
        return { data, total };
    }
    async unqualityTypeChart(pie, request) {
        const builder = this.getBaseBuilder(request);
        const total = pie === "1" ? await builder.getCount() : 0;
        builder.leftJoin(`${builder.alias}.reasons`, "r");
        builder
            .andWhere("r.delete is false")
            .andWhere("r.unqualityType is not null");
        builder
            .select("r.unqualityType", "type")
            .addSelect(`count(distinct ${builder.alias}.id)`, "value")
            .groupBy("r.unqualityType");
        if (pie === "0") {
            builder.addSelect(`DATE_FORMAT(${builder.alias}.created_on,'%Y-%m')`, "month");
            builder.addGroupBy(`DATE_FORMAT(${builder.alias}.created_on,'%Y-%m')`);
        }
        const data = await builder.getRawMany();
        return { data, total };
    }
    async reasonCategoryChart(request) {
        const builder = this.getBaseBuilder(request);
        builder.leftJoin(`${builder.alias}.reasons`, "r");
        builder.andWhere("r.delete is false");
        builder
            .select("r.category", "type")
            .addSelect(`count(distinct ${builder.alias}.id)`, "value")
            .groupBy("r.category");
        const data = await builder.getRawMany();
        return { data };
    }
    async reasonSubCategoryChart(category, request) {
        const builder = this.getBaseBuilder(request);
        builder.leftJoin(`${builder.alias}.reasons`, "r");
        builder.andWhere("r.delete is false");
        builder.andWhere("r.category = :category", { category });
        builder
            .select("r.subCategory", "type")
            .addSelect(`count(distinct ${builder.alias}.id)`, "value")
            .groupBy("r.subCategory");
        const data = await builder.getRawMany();
        return { data };
    }
    async chartData(request) {
        const builder = this.getBaseBuilder(request, true);
        builder.leftJoinAndSelect(`${builder.alias}.reasons`, "r", "r.delete is false");
        builder.leftJoinAndSelect(`r.configs`, "c");
        return builder.getMany();
    }
};
exports.DashboardController = DashboardController;
__decorate([
    (0, common_1.Get)("panelCount"),
    (0, public_decorator_1.Public)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "panel", null);
__decorate([
    (0, common_1.Get)("processing"),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "processing", null);
__decorate([
    (0, common_1.Get)("todo"),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "todo", null);
__decorate([
    (0, common_1.Get)("panel/data/:status"),
    (0, public_decorator_1.Public)(),
    __param(0, (0, common_1.Param)("status")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "panelData", null);
__decorate([
    (0, common_1.Get)("processing/data"),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "processingData", null);
__decorate([
    (0, common_1.Post)("chart/step/:pie"),
    (0, public_decorator_1.Public)(),
    __param(0, (0, common_1.Param)("pie")),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "stepChart", null);
__decorate([
    (0, common_1.Post)("chart/customer/:pie"),
    (0, public_decorator_1.Public)(),
    __param(0, (0, common_1.Param)("pie")),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "customerChart", null);
__decorate([
    (0, common_1.Post)("chart/unqualityType/:pie"),
    (0, public_decorator_1.Public)(),
    __param(0, (0, common_1.Param)("pie")),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "unqualityTypeChart", null);
__decorate([
    (0, common_1.Post)("chart/category"),
    (0, public_decorator_1.Public)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "reasonCategoryChart", null);
__decorate([
    (0, common_1.Post)("chart/category/:category"),
    (0, public_decorator_1.Public)(),
    __param(0, (0, common_1.Param)("category")),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "reasonSubCategoryChart", null);
__decorate([
    (0, common_1.Post)("chart/data"),
    (0, public_decorator_1.Public)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "chartData", null);
exports.DashboardController = DashboardController = __decorate([
    (0, common_1.Controller)("dashboard"),
    __metadata("design:paramtypes", [dashboard_service_1.DashboardService,
        problem_service_1.ProblemService,
        reason_service_1.ReasonService])
], DashboardController);
//# sourceMappingURL=dashboard.controller.js.map