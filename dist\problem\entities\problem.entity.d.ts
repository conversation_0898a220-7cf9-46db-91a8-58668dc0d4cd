import { Relation } from "typeorm";
import { Attachment, CodedEntity, NumberIdTimeObject, ProblemDescription } from "../../framework/model/base";
import { ProblemStatus } from "./constant";
import { ProblemOperateLog } from "./problem-operate-log.entity";
import { Reason } from "./reason.entity";
export declare class Problem extends NumberIdTimeObject implements CodedEntity<number> {
    code: string;
    createdOn: Date;
    machineCategory: string;
    machineType: string;
    customer: string;
    projectCode: string;
    productLine: string;
    factory: string;
    businessUnit: string;
    creatorId: number;
    creatorName: string;
    productStep: string;
    status: ProblemStatus;
    description: string;
    descriptions: ProblemDescription;
    goodPartImages: Attachment[];
    badPartImages: Attachment[];
    workOrderCode: string;
    workOrderNum: number;
    reasons: Relation<Reason>[];
    logs: Relation<ProblemOperateLog>[];
    cqeId: number;
    cqeName: string;
    todoId: number;
}
