"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PuppeteerCoreModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PuppeteerCoreModule = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const puppeteer_core_1 = require("puppeteer-core");
const common_2 = require("./common");
const puppeteer_constants_1 = require("./puppeteer.constants");
let PuppeteerCoreModule = PuppeteerCoreModule_1 = class PuppeteerCoreModule {
    options;
    moduleRef;
    logger = new common_1.Logger("PuppeteerModule");
    constructor(options, moduleRef) {
        this.options = options;
        this.moduleRef = moduleRef;
    }
    static forRoot(options = {}) {
        const puppeteerModuleOptions = {
            provide: puppeteer_constants_1.PUPPETEER_MODULE_OPTIONS,
            useValue: options,
        };
        const pluginProvider = {
            provide: puppeteer_constants_1.PUPPETEER_BROWSER_PLUGINS,
            useFactory: async (options) => {
                return [];
            },
            inject: [puppeteer_constants_1.PUPPETEER_MODULE_OPTIONS],
        };
        const browserProvider = {
            provide: (0, common_2.getBrowserToken)(options),
            useFactory: async (options) => {
                return await (0, puppeteer_core_1.launch)(options);
            },
            inject: [puppeteer_constants_1.PUPPETEER_MODULE_OPTIONS],
        };
        const providers = [puppeteerModuleOptions, pluginProvider, browserProvider];
        const exports = [browserProvider];
        return {
            module: PuppeteerCoreModule_1,
            providers,
            exports,
        };
    }
    static forRootAsync(options) {
        const pluginProvider = {
            provide: puppeteer_constants_1.PUPPETEER_BROWSER_PLUGINS,
            useFactory: async (options) => {
                return [];
            },
            inject: [puppeteer_constants_1.PUPPETEER_MODULE_OPTIONS],
        };
        const browserProvider = {
            provide: (0, common_2.getBrowserToken)(options),
            useFactory: async (options) => {
                return await (0, puppeteer_core_1.launch)(options);
            },
            inject: [puppeteer_constants_1.PUPPETEER_MODULE_OPTIONS],
        };
        const asyncProviders = this.createAsyncProviders(options);
        const providers = [...asyncProviders, pluginProvider, browserProvider];
        const exports = [browserProvider];
        return {
            module: PuppeteerCoreModule_1,
            providers,
            exports,
        };
    }
    async onApplicationShutdown() {
        const browser = this.moduleRef.get((0, common_2.getBrowserToken)(this.options));
        try {
            if (browser && browser.connected) {
                this.logger.log("Closing browser...");
                await browser.close();
            }
        }
        catch (e) {
            this.logger.error(e?.message);
        }
    }
    static createAsyncProviders(options) {
        if (options.useExisting || options.useFactory) {
            return [this.createAsyncOptionsProvider(options)];
        }
        const useClass = options.useClass;
        return [
            this.createAsyncOptionsProvider(options),
            {
                provide: useClass,
                useClass,
            },
        ];
    }
    static createAsyncOptionsProvider(options) {
        if (options.useFactory) {
            return {
                provide: puppeteer_constants_1.PUPPETEER_MODULE_OPTIONS,
                useFactory: options.useFactory,
                inject: options.inject || [],
            };
        }
        const inject = [
            (options.useClass ||
                options.useExisting),
        ];
        return {
            provide: puppeteer_constants_1.PUPPETEER_MODULE_OPTIONS,
            useFactory: async (optionsFactory) => await optionsFactory.createPuppeteerOptions(options.name),
            inject,
        };
    }
};
exports.PuppeteerCoreModule = PuppeteerCoreModule;
exports.PuppeteerCoreModule = PuppeteerCoreModule = PuppeteerCoreModule_1 = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({}),
    __param(0, (0, common_1.Inject)(puppeteer_constants_1.PUPPETEER_MODULE_OPTIONS)),
    __metadata("design:paramtypes", [Object, core_1.ModuleRef])
], PuppeteerCoreModule);
//# sourceMappingURL=puppeteer-core.module.js.map