"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DictionaryRelation = exports.Dictionary = exports.DictionaryCategoryOpions = exports.DictionaryCategory = void 0;
const typeorm_1 = require("typeorm");
const base_1 = require("../../framework/model/base");
var DictionaryCategory;
(function (DictionaryCategory) {
    DictionaryCategory["CUSTOMER"] = "CUSTOMER";
    DictionaryCategory["BUSINESS_UNIT"] = "BUSINESS_UNIT";
    DictionaryCategory["FACTORY"] = "FACTORY";
    DictionaryCategory["PRODUCT_LINE"] = "PRODUCT_LINE";
    DictionaryCategory["MACHINE_TYPE"] = "MACHINE_TYPE";
    DictionaryCategory["PRODUCT_STEP"] = "PRODUCT_STEP";
    DictionaryCategory["UNQUALITY_TYPE"] = "UNQUALITY_TYPE";
    DictionaryCategory["UNQUALITY_CODE"] = "UNQUALITY_CODE";
    DictionaryCategory["REASON_TYPE"] = "REASON_TYPE";
})(DictionaryCategory || (exports.DictionaryCategory = DictionaryCategory = {}));
exports.DictionaryCategoryOpions = {
    [DictionaryCategory.CUSTOMER]: "客户",
    [DictionaryCategory.BUSINESS_UNIT]: "事业部",
    [DictionaryCategory.FACTORY]: "工厂",
    [DictionaryCategory.PRODUCT_LINE]: "生产线",
    [DictionaryCategory.MACHINE_TYPE]: "机型",
    [DictionaryCategory.PRODUCT_STEP]: "产品阶段",
    [DictionaryCategory.UNQUALITY_TYPE]: "不良类别",
    [DictionaryCategory.UNQUALITY_CODE]: "不良代码",
    [DictionaryCategory.REASON_TYPE]: "原因类别",
};
let Dictionary = class Dictionary extends base_1.NumberIdObject {
    name;
    description;
    category;
    options;
    priority;
    tree;
};
exports.Dictionary = Dictionary;
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Dictionary.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Dictionary.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: DictionaryCategory,
        default: DictionaryCategory.CUSTOMER,
    }),
    __metadata("design:type", String)
], Dictionary.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)("json"),
    __metadata("design:type", Array)
], Dictionary.prototype, "options", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], Dictionary.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Dictionary.prototype, "tree", void 0);
exports.Dictionary = Dictionary = __decorate([
    (0, typeorm_1.Entity)()
], Dictionary);
let DictionaryRelation = class DictionaryRelation extends base_1.NumberIdObject {
    machineType;
    customer;
    businessUnit;
};
exports.DictionaryRelation = DictionaryRelation;
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], DictionaryRelation.prototype, "machineType", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], DictionaryRelation.prototype, "customer", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], DictionaryRelation.prototype, "businessUnit", void 0);
exports.DictionaryRelation = DictionaryRelation = __decorate([
    (0, typeorm_1.Entity)()
], DictionaryRelation);
//# sourceMappingURL=dictionary.entity.js.map