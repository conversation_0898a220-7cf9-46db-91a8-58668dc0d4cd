{"name": "@css-inline/css-inline", "version": "0.13.0", "description": "High-performance library for inlining CSS into HTML 'style' attributes", "main": "index.js", "repository": "https://github.com/Stranger6667/css-inline", "keywords": ["css", "html", "email", "stylesheet", "inlining"], "files": ["index.d.ts", "index.js", "js-binding.js", "js-binding.d.ts"], "types": "index.d.ts", "napi": {"name": "css-inline", "triples": {"additional": ["aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "aarch64-unknown-linux-musl", "armv7-unknown-linux-gnueabihf", "x86_64-unknown-linux-musl"]}}, "license": "MIT", "devDependencies": {"@ava/typescript": "^4.1.0", "@napi-rs/cli": "^2.17.0", "@swc-node/register": "^1.6.8", "@swc/core": "^1.3.101", "@types/copyfiles": "^2", "@types/eslint": "^8", "@typescript-eslint/eslint-plugin": "^6.16.0", "@typescript-eslint/parser": "^6.16.0", "ava": "^6.0.1", "benny": "^3.7.1", "copyfiles": "^2.4.1", "dts-bundle-generator": "^9.1.0", "esbuild": "^0.19.10", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.2", "eslint-plugin-sonarjs": "^0.23.0", "inline-css": "^4.0.2", "juice": "^10.0.0", "npm-run-all2": "^6.1.1", "prettier": "^3.1.1", "typescript": "^5.3.3"}, "ava": {"require": ["@swc-node/register"], "typescript": {"rewritePaths": {"src/": "build/"}, "compile": false}, "timeout": "3m"}, "engines": {"node": ">= 10"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "scripts": {"artifacts": "napi artifacts", "bench": "node -r @swc-node/register benches/bench.ts", "bundle": "run-p 'bundle:*'", "bundle:js": "node bundle.js", "bundle:dts": "dts-bundle-generator --external-types -o wasm/index.d.ts wasm-binding.ts", "build": "napi build --platform --release --js js-binding.js --dts js-binding.d.ts", "build:debug": "napi build --platform --js js-binding.js --dts js-binding.d.ts", "build:wasm": "run-s build:wasm-web copy-wasm bundle", "build:wasm-web": "wasm-pack build --target web --out-name index --out-dir wasm/dist --release", "copy-wasm": "copyfiles -f wasm/dist/index_bg.wasm ./wasm", "lint": "eslint . -c ./.eslintrc.yml './**/*.{ts,tsx,js}'", "lint:fix": "eslint . -c ./.eslintrc.yml './**/*.{ts,tsx,js}' --fix", "prepublishOnly": "napi prepublish -t npm", "test": "ava __test__/index*.*", "test:wasm": "ava __test__/**/wasm*.*", "universal": "napi universal", "version": "napi version"}, "packageManager": "yarn@4.0.2", "optionalDependencies": {"@css-inline/css-inline-win32-x64-msvc": "0.13.0", "@css-inline/css-inline-darwin-x64": "0.13.0", "@css-inline/css-inline-linux-x64-gnu": "0.13.0", "@css-inline/css-inline-darwin-arm64": "0.13.0", "@css-inline/css-inline-linux-arm64-gnu": "0.13.0", "@css-inline/css-inline-linux-arm64-musl": "0.13.0", "@css-inline/css-inline-linux-arm-gnueabihf": "0.13.0", "@css-inline/css-inline-linux-x64-musl": "0.13.0"}}