import { Strategy } from "passport-jwt";
import { AuthService } from "./auth.service";
export type Payload = {
    token: string;
    id: number;
    name: string;
};
declare const JwtStrategy_base: new (...args: any[]) => Strategy;
export declare class JwtStrategy extends JwtStrategy_base {
    private authService;
    constructor(authService: AuthService);
    validate(payload: Payload): Promise<Payload>;
}
export {};
