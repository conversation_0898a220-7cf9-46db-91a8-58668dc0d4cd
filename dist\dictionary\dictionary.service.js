"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DictionaryService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const base_service_1 = require("../framework/service/base.service");
const dictionary_entity_1 = require("./entities/dictionary.entity");
let DictionaryService = class DictionaryService extends base_service_1.BaseService {
    constructor(repository, dataSource) {
        super(repository, dataSource);
    }
    processUpdate(source, target) {
        target.priority = source.priority;
        target.description = source.description;
        if (target.category !== dictionary_entity_1.DictionaryCategory.FACTORY) {
            target.options = source.options;
        }
    }
    async query(request) {
        return super.query(request);
    }
    async init() {
        const count = await this.repository.count();
        if (count === 0) {
            const initData = [
                {
                    category: dictionary_entity_1.DictionaryCategory.CUSTOMER,
                    priority: 0,
                    name: "客户",
                    description: "定义客户相关信息, 包括客户名称,工厂,目标值",
                    options: [{ name: "客户1" }, { name: "客户2" }, { name: "客户3" }],
                },
                {
                    category: dictionary_entity_1.DictionaryCategory.BUSINESS_UNIT,
                    priority: 1,
                    name: "事业部",
                    description: "定义事业部相关信息",
                    options: [
                        { name: "BU1" },
                        { name: "BU2" },
                        { name: "BU3" },
                        { name: "BU4" },
                    ],
                },
                {
                    category: dictionary_entity_1.DictionaryCategory.FACTORY,
                    priority: 3,
                    name: "和而泰工厂",
                    description: "定义和而泰工厂",
                    options: [
                        { name: "深圳工厂" },
                        { name: "越南工厂" },
                        { name: "苏州工厂" },
                    ],
                },
                {
                    category: dictionary_entity_1.DictionaryCategory.PRODUCT_LINE,
                    priority: 4,
                    name: "生产线",
                    description: "定义生产线",
                    options: [{ name: "产线1" }, { name: "产线2" }, { name: "产线3" }],
                },
                {
                    category: dictionary_entity_1.DictionaryCategory.MACHINE_TYPE,
                    priority: 5,
                    name: "机型",
                    description: "定义机型",
                    options: [{ name: "机型1" }, { name: "机型2" }, { name: "机型3" }],
                },
                {
                    category: dictionary_entity_1.DictionaryCategory.PRODUCT_STEP,
                    priority: 0,
                    name: "产品阶段",
                    description: "定义产品阶段",
                    options: [{ name: "EVT" }, { name: "MP" }],
                },
                {
                    category: dictionary_entity_1.DictionaryCategory.UNQUALITY_TYPE,
                    priority: 0,
                    name: "不良类别",
                    description: "定义不良类别",
                    options: [
                        { name: "DFT" },
                        { name: "DFM" },
                        { name: "DFA" },
                        { name: "DFS" },
                        { name: "物料问题" },
                        { name: "制程问题" },
                    ],
                },
                {
                    category: dictionary_entity_1.DictionaryCategory.REASON_TYPE,
                    priority: 0,
                    name: "原因类别",
                    description: "定义问题原因类别",
                    tree: true,
                    options: [
                        {
                            name: "设计",
                            children: [
                                { name: "设计1" },
                                { name: "设计2" },
                                { name: "设计3" },
                                { name: "设计4" },
                            ],
                        },
                        {
                            name: "制造",
                            children: [
                                { name: "制造1" },
                                { name: "制造2" },
                                { name: "制造3" },
                                { name: "制造4" },
                            ],
                        },
                        {
                            name: "来料",
                            children: [
                                { name: "来料1" },
                                { name: "来料2" },
                                { name: "来料3" },
                                { name: "来料4" },
                            ],
                        },
                        {
                            name: "测试",
                            children: [
                                { name: "测试1" },
                                { name: "测试2" },
                                { name: "测试3" },
                                { name: "测试4" },
                            ],
                        },
                    ],
                },
            ];
            await this.execute(manager => {
                return manager.save(initData.map(item => this.repository.create(item)));
            });
        }
    }
    async getOptions(categories = []) {
        const result = {};
        if (categories.length) {
            const dictionaries = await this.repository.findBy({
                category: (0, typeorm_2.In)(categories),
            });
            for (const dictionary of dictionaries) {
                result[dictionary.category] = dictionary.options;
            }
        }
        return result;
    }
    async getCategoryOptions(category) {
        const dictionary = await this.repository.findOneBy({
            category: (0, typeorm_2.Equal)(category),
        });
        return dictionary ? dictionary.options : [];
    }
    async getDictionaryByCategory(category) {
        return await this.repository.findOneBy({
            category: (0, typeorm_2.Equal)(category),
        });
    }
};
exports.DictionaryService = DictionaryService;
exports.DictionaryService = DictionaryService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(dictionary_entity_1.Dictionary)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource])
], DictionaryService);
//# sourceMappingURL=dictionary.service.js.map