export declare enum NodeState {
    ANALYZE = "ANALYZE",
    ANALYZE_AUDIT = "ANALYZE_AUDIT",
    VALIDATE = "VALIDATE",
    VALIDATE_AUDIT = "VALIDATE_AUDIT",
    CQE_AUDIT = "CQE_AUDIT",
    COMPLETE = "COMPLETE"
}
export declare const PRIVATE_KEY = "F5934C14C0F0193CA1D0344AF9C99739";
export type Node = {
    index: number;
    state: NodeState;
    next?: NodeState;
};
export declare const nodes: Node[];
export declare enum ProblemStatus {
    DRAFT = "DRAFT",
    CQE = "CQE",
    NEW = "NEW",
    PROCESSING = "PROCESSING",
    CLOSED = "CLOSED",
    OBSOLETE = "OBSOLETE"
}
export declare enum ReasonDetailType {
    PRODUCE = "PRODUCE",
    EXPOSE = "EXPOSE",
    SYSTEM = "SYSTEM"
}
export declare enum ReasonStatus {
    OPEN = "OPEN",
    FOLLOW = "FOLLOW",
    CLOSED = "CLOSED",
    REJECTED = "REJECTED"
}
