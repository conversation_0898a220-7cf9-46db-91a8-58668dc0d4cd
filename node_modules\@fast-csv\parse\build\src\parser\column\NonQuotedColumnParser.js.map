{"version": 3, "file": "NonQuotedColumnParser.js", "sourceRoot": "", "sources": ["../../../../src/parser/column/NonQuotedColumnParser.ts"], "names": [], "mappings": ";;;AACA,uDAAoD;AAEpD,oCAAiC;AAEjC,MAAa,qBAAqB;IAK9B,YAAmB,aAA4B;QAC3C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,CAAC,aAAa,CAAC,CAAC;IAC9D,CAAC;IAEM,KAAK,CAAC,OAAgB;QACzB,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;YAC5B,OAAO,IAAI,CAAC;SACf;QACD,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC;QAC/B,MAAM,UAAU,GAAG,EAAE,CAAC;QACtB,IAAI,SAAS,GAAG,OAAO,CAAC,kBAAkB,CAAC;QAC3C,OAAO,SAAS,EAAE,SAAS,GAAG,OAAO,CAAC,kBAAkB,EAAE;YACtD,IAAI,aAAK,CAAC,gBAAgB,CAAC,SAAS,EAAE,aAAa,CAAC,IAAI,aAAK,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE;gBAC1F,MAAM;aACT;YACD,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACjC,OAAO,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;SACvC;QACD,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5D,CAAC;CACJ;AA1BD,sDA0BC"}