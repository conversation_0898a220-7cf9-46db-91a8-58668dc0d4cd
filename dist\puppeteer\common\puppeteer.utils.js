"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getBrowserToken = getBrowserToken;
exports.getPageToken = getPageToken;
exports.getBrowserPrefix = getBrowserPrefix;
const puppeteer_core_1 = require("puppeteer-core");
const puppeteer_constants_1 = require("../puppeteer.constants");
function getBrowserToken(browser = puppeteer_constants_1.DEFAULT_BROWSER_NAME) {
    return puppeteer_constants_1.DEFAULT_BROWSER_NAME === browser
        ? puppeteer_core_1.Browser
        : "string" === typeof browser
            ? `${browser}Browser`
            : puppeteer_constants_1.DEFAULT_BROWSER_NAME === browser.name || !browser.name
                ? puppeteer_core_1.Browser
                : `${browser.name}Browser`;
}
function getPageToken(page, browser = puppeteer_constants_1.DEFAULT_BROWSER_NAME) {
    const browserPrefix = getBrowserPrefix(browser);
    return `${browserPrefix}${page}Page`;
}
function getBrowserPrefix(browser = puppeteer_constants_1.DEFAULT_BROWSER_NAME) {
    return puppeteer_constants_1.DEFAULT_BROWSER_NAME === browser
        ? ""
        : "string" === typeof browser
            ? `${browser}_`
            : puppeteer_constants_1.DEFAULT_BROWSER_NAME === browser.name || !browser.name
                ? ""
                : `${browser.name}_`;
}
//# sourceMappingURL=puppeteer.utils.js.map