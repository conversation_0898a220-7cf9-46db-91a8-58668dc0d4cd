"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReasonService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const base_service_1 = require("../framework/service/base.service");
const typeorm_2 = require("typeorm");
const reason_entity_1 = require("./entities/reason.entity");
const constant_1 = require("./entities/constant");
let ReasonService = class ReasonService extends base_service_1.BaseService {
    constructor(repository, dataSource) {
        super(repository, dataSource);
    }
    async canFinish(reason) {
        const unComplete = await this.repository
            .createQueryBuilder("r")
            .where("r.problem.id = :pid", { pid: reason.problem.id })
            .andWhere("r.delete is false")
            .andWhere("r.state != :state", { state: constant_1.NodeState.COMPLETE })
            .getCount();
        return unComplete === 0;
    }
    findByProblemId(id, relations = ["configs", "details"]) {
        return this.repository.find({
            where: { problem: { id }, delete: false },
            order: { id: "ASC" },
            relations,
        });
    }
    assignSkipFields() {
        return [
            ...super.assignSkipFields(),
            "problem",
            "stateIdx",
            "details",
            "configs",
            "status",
            "state",
            "delete",
            "finishOn",
            "deleteById",
            "deleteByName",
        ];
    }
};
exports.ReasonService = ReasonService;
exports.ReasonService = ReasonService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(reason_entity_1.Reason)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource])
], ReasonService);
//# sourceMappingURL=reason.service.js.map