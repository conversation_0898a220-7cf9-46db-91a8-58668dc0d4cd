"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeRuleModule = void 0;
const common_1 = require("@nestjs/common");
const code_rule_service_1 = require("./code-rule.service");
const code_rule_controller_1 = require("./code-rule.controller");
const typeorm_1 = require("@nestjs/typeorm");
const code_rule_entity_1 = require("./entities/code-rule.entity");
let CodeRuleModule = class CodeRuleModule {
};
exports.CodeRuleModule = CodeRuleModule;
exports.CodeRuleModule = CodeRuleModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([code_rule_entity_1.CodeRule])],
        controllers: [code_rule_controller_1.CodeRuleController],
        providers: [code_rule_service_1.CodeRuleService],
        exports: [code_rule_service_1.CodeRuleService],
    })
], CodeRuleModule);
//# sourceMappingURL=code-rule.module.js.map