import { BaseService } from "src/framework/service/base.service";
import { DataSource, Repository } from "typeorm";
import { ReasonDetail } from "./entities/reason-detail.entity";
export declare class ReasonDetailService extends BaseService<ReasonDetail> {
    constructor(repository: Repository<ReasonDetail>, dataSource: DataSource);
    assignSkipFields(): string[];
    findByReasonId(id: number): Promise<ReasonDetail[]>;
}
