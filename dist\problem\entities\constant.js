"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReasonStatus = exports.ReasonDetailType = exports.ProblemStatus = exports.nodes = exports.PRIVATE_KEY = exports.NodeState = void 0;
var NodeState;
(function (NodeState) {
    NodeState["ANALYZE"] = "ANALYZE";
    NodeState["ANALYZE_AUDIT"] = "ANALYZE_AUDIT";
    NodeState["VALIDATE"] = "VALIDATE";
    NodeState["VALIDATE_AUDIT"] = "VALIDATE_AUDIT";
    NodeState["CQE_AUDIT"] = "CQE_AUDIT";
    NodeState["COMPLETE"] = "COMPLETE";
})(NodeState || (exports.NodeState = NodeState = {}));
exports.PRIVATE_KEY = "F5934C14C0F0193CA1D0344AF9C99739";
exports.nodes = [
    { index: 0, state: NodeState.ANALYZE, next: NodeState.ANALYZE_AUDIT },
    { index: 1, state: NodeState.ANALYZE_AUDIT, next: NodeState.VALIDATE },
    { index: 2, state: NodeState.VALIDATE, next: NodeState.VALIDATE_AUDIT },
    { index: 3, state: NodeState.VALIDATE_AUDIT, next: NodeState.CQE_AUDIT },
    { index: 4, state: NodeState.CQE_AUDIT, next: NodeState.COMPLETE },
    { index: 5, state: NodeState.COMPLETE },
];
var ProblemStatus;
(function (ProblemStatus) {
    ProblemStatus["DRAFT"] = "DRAFT";
    ProblemStatus["CQE"] = "CQE";
    ProblemStatus["NEW"] = "NEW";
    ProblemStatus["PROCESSING"] = "PROCESSING";
    ProblemStatus["CLOSED"] = "CLOSED";
    ProblemStatus["OBSOLETE"] = "OBSOLETE";
})(ProblemStatus || (exports.ProblemStatus = ProblemStatus = {}));
var ReasonDetailType;
(function (ReasonDetailType) {
    ReasonDetailType["PRODUCE"] = "PRODUCE";
    ReasonDetailType["EXPOSE"] = "EXPOSE";
    ReasonDetailType["SYSTEM"] = "SYSTEM";
})(ReasonDetailType || (exports.ReasonDetailType = ReasonDetailType = {}));
var ReasonStatus;
(function (ReasonStatus) {
    ReasonStatus["OPEN"] = "OPEN";
    ReasonStatus["FOLLOW"] = "FOLLOW";
    ReasonStatus["CLOSED"] = "CLOSED";
    ReasonStatus["REJECTED"] = "REJECTED";
})(ReasonStatus || (exports.ReasonStatus = ReasonStatus = {}));
//# sourceMappingURL=constant.js.map