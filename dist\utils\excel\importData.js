"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExcelEntityPopulator = exports.SheetNotExistsError = void 0;
const exceljs_1 = __importDefault(require("exceljs"));
class SheetNotExistsError extends Error {
}
exports.SheetNotExistsError = SheetNotExistsError;
class ExcelEntityPopulator {
    workbook;
    _sheets;
    file;
    errors;
    constructor(file) {
        this.workbook = new exceljs_1.default.Workbook();
        this.file = file;
        this.errors = [];
    }
    async init() {
        this._sheets = {};
        if (typeof this.file === "string") {
            this.workbook = await this.workbook.xlsx.readFile(this.file);
        }
        else {
            this.workbook = await this.workbook.xlsx.load(this.file);
        }
        this.workbook.worksheets.forEach(sheet => (this._sheets[sheet.name] = sheet));
    }
    get sheetNames() {
        return Object.keys(this._sheets);
    }
    get sheets() {
        return Object.values(this._sheets);
    }
    get hasError() {
        return this.errors.length > 0;
    }
    doImport(sheetNameOrIndex, header = {}, validators = []) {
        let sheet;
        if (typeof sheetNameOrIndex === "string") {
            sheet = this._sheets[sheetNameOrIndex];
        }
        else {
            sheet = this.sheets[sheetNameOrIndex];
        }
        if (sheet) {
            const headers = [];
            const rows = [];
            sheet.eachRow({ includeEmpty: true }, (row, idx) => {
                if (idx === 1) {
                    row.eachCell({ includeEmpty: true }, (cell, col) => {
                        if (cell.value !== null &&
                            !!header[cell.value.toString()]?.length) {
                            headers[col] = header[cell.value.toString()];
                        }
                    });
                }
                else {
                    const item = {};
                    for (const idx of Object.keys(headers)) {
                        const cell = row.getCell(parseInt(idx, 10));
                        const key = headers[idx];
                        item[key] = cell.value;
                        for (const validator of validators) {
                            const msg = validator(row, cell, key);
                            if (!!msg?.length) {
                                cell.note = msg;
                                cell.style = {
                                    fill: {
                                        type: "pattern",
                                        pattern: "solid",
                                        fgColor: { argb: "FFFF4D4F" },
                                    },
                                };
                                this.errors.push({ cell, msg });
                            }
                        }
                    }
                    if (!!Object.keys(item).length) {
                        rows.push(item);
                    }
                }
            });
            return rows;
        }
        throw new SheetNotExistsError("sheet not exists");
    }
    outputErrors(filename, response) {
        response.contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", `attachment; filename=${encodeURIComponent(filename)}`);
        return this.workbook.xlsx.write(response).then(function () {
            response.status(200).end();
        });
    }
}
exports.ExcelEntityPopulator = ExcelEntityPopulator;
//# sourceMappingURL=importData.js.map