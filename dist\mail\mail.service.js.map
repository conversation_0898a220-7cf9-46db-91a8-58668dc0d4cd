{"version": 3, "file": "mail.service.js", "sourceRoot": "", "sources": ["../../src/mail/mail.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,mDAAuD;AACvD,2CAAoD;AACpD,mDAAyC;AACzC,2DAA0D;AAG1D,oDAA8C;AAC9C,6DAA8D;AAGvD,IAAM,WAAW,GAAjB,MAAM,WAAW;IAEZ;IACA;IAFV,YACU,aAA4B,EAC5B,OAAgB;QADhB,kBAAa,GAAb,aAAa,CAAe;QAC5B,YAAO,GAAP,OAAO,CAAS;IACvB,CAAC;IAEJ,KAAK,CAAC,aAAa,CACjB,KAA+D,EAC/D,OAAyB,OAAO;QAEhC,MAAM,GAAG,GAAG,GAAG,mBAAM,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;QACtC,MAAM,MAAM,GAAG,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;QAC7C,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,IAAI,CAAC,aAAa;iBACf,QAAQ,CAAC;gBACR,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,EAAE,EAAE,CAAC,GAAG,mBAAM,CAAC,WAAW,CAAC;gBAC3B,OAAO,EAAE,UAAU,mBAAM,CAAC,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,UAAU,MAAM,EAAE;gBAC3E,QAAQ,EAAE,OAAO;gBACjB,OAAO,EAAE;oBACP,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE;oBACpB,GAAG,EAAE,mBAAM,CAAC,OAAO;oBACnB,OAAO,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC,IAAI,IAC9B,mBAAM,CAAC,OACT,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM,EAAE;oBACtD,GAAG;iBACJ;aACF,CAAC;iBACD,KAAK,CAAC,CAAC,CAAC,EAAE;gBACT,eAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,SAAS,CACb,OAAiB,EACjB,KAAgB,EAChB,OAAgB,EAChB,MAAe,KAAK;QAEpB,IAAI,KAAa,CAAC;QAClB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAC1C,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QACtD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,IAAI,CACb,GAAG,mBAAM,CAAC,MAAM,4BAA4B,OAAO,CAAC,EAAE,EAAE,EACxD;gBACE,SAAS,EAAE,kBAAkB;aAC9B,CACF,CAAC;YACF,MAAM,IAAI,CAAC,iBAAiB,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;YACjD,MAAM,IAAI,CAAC,kBAAkB,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;YAElD,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;YACrB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YACzC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM,WAAW,CAAC,WAAW,EAAE,CAAC;YAC1D,MAAM,IAAI,CAAC,WAAW,CAAC;gBACrB,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;gBACvB,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;gBACzB,iBAAiB,EAAE,CAAC;aACrB,CAAC,CAAC;YACH,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC;gBAC5B,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;QACL,CAAC;gBAAS,CAAC;YACT,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC;QAED,MAAM,EAAE,GAAG,EAAE,CAAC;QACd,MAAM,EAAE,GAAG,CAAC,GAAG,mBAAM,CAAC,WAAW,CAAC,CAAC;QACnC,IAAI,KAAK,KAAK,oBAAS,CAAC,OAAO,EAAE,CAAC;YAChC,MAAM,QAAQ,GAAG,kBAAkB,CAAC,kBAAkB,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YACpE,MAAM,GAAG,GAAG,GAAG,mBAAM,CAAC,SAAS,aAAa,QAAQ,EAAE,CAAC;YACvD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,MAAM,MAAM,GACV,MAAM,CAAC,MAAM,KAAK,uBAAY,CAAC,QAAQ;oBACrC,CAAC,CAAC,KAAK;oBACP,CAAC,CAAC,aAAa,CAAC;gBACpB,MAAM,CAAC,OAAO;qBACX,MAAM,CACL,UAAU,CAAC,EAAE,CACX,UAAU,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,CAAC,UAAU,CAAC,UAAU,EAAE,MAAM,CAChE;qBACA,OAAO,CAAC,UAAU,CAAC,EAAE;oBACpB,IAAI,CAAC,aAAa;yBACf,QAAQ,CAAC;wBACR,EAAE,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;wBAC3B,EAAE;wBACF,OAAO,EAAE,UAAU,mBAAM,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK;wBACtD,QAAQ,EAAE,QAAQ;wBAClB,OAAO,EAAE;4BACP,IAAI,EAAE,GAAG,UAAU,CAAC,SAAS,EAAE;4BAC/B,GAAG,EAAE,mBAAM,CAAC,OAAO;4BACnB,OAAO,EAAE,OAAO,mBAAM,CAAC,OAAO,KAAK,MAAM,EAAE;4BAC3C,KAAK;4BACL,GAAG;4BACH,OAAO,EAAE,OAAO,CAAC,WAAW;yBAC7B;qBACF,CAAC;yBACD,KAAK,CAAC,CAAC,CAAC,EAAE;wBACT,eAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;oBAC7B,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;YACP,CAAC;QACH,CAAC;aAAM,IAAI,KAAK,KAAK,oBAAS,CAAC,QAAQ,EAAE,CAAC;YACxC,MAAM,QAAQ,GAAG,kBAAkB,CAAC,kBAAkB,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YACpE,MAAM,GAAG,GAAG,GAAG,mBAAM,CAAC,SAAS,aAAa,QAAQ,EAAE,CAAC;YACvD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAC9B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,KAAK,oBAAS,CAAC,OAAO,CACzC,CAAC;gBACF,MAAM,CAAC,OAAO;qBACX,MAAM,CACL,UAAU,CAAC,EAAE,CACX,UAAU,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,CAAC,UAAU,CAAC,UAAU,EAAE,MAAM,CAChE;qBACA,OAAO,CAAC,UAAU,CAAC,EAAE;oBACpB,IAAI,CAAC,aAAa;yBACf,QAAQ,CAAC;wBACR,EAAE,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;wBAC3B,EAAE;wBACF,OAAO,EAAE,UAAU,mBAAM,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK;wBACtD,QAAQ,EAAE,QAAQ;wBAClB,OAAO,EAAE;4BACP,IAAI,EAAE,GAAG,UAAU,CAAC,SAAS,EAAE;4BAC/B,OAAO,EAAE,OAAO,mBAAM,CAAC,OAAO,YAAY;4BAC1C,GAAG,EAAE,mBAAM,CAAC,OAAO;4BACnB,KAAK;4BACL,GAAG;4BACH,OAAO,EAAE,GAAG,IAAI,EAAE,SAAS,EAAE;yBAC9B;qBACF,CAAC;yBACD,KAAK,CAAC,CAAC,CAAC,EAAE;wBACT,eAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;oBAC7B,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;YACP,CAAC;QACH,CAAC;aAAM,IAAI,KAAK,KAAK,oBAAS,CAAC,aAAa,EAAE,CAAC;YAC7C,MAAM,QAAQ,GAAG,kBAAkB,CAAC,mBAAmB,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YACrE,MAAM,GAAG,GAAG,GAAG,mBAAM,CAAC,SAAS,aAAa,QAAQ,EAAE,CAAC;YACvD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAC9B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,KAAK,oBAAS,CAAC,OAAO,CACzC,CAAC;gBACF,MAAM,CAAC,OAAO;qBACX,MAAM,CACL,UAAU,CAAC,EAAE,CACX,UAAU,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,CAAC,UAAU,CAAC,UAAU,EAAE,MAAM,CAChE;qBACA,OAAO,CAAC,UAAU,CAAC,EAAE;oBACpB,IAAI,CAAC,aAAa;yBACf,QAAQ,CAAC;wBACR,EAAE,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;wBAC3B,EAAE;wBACF,OAAO,EAAE,UAAU,mBAAM,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,GAC/C,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KACtB,EAAE;wBACF,QAAQ,EAAE,QAAQ;wBAClB,OAAO,EAAE;4BACP,IAAI,EAAE,GAAG,UAAU,CAAC,SAAS,EAAE;4BAC/B,OAAO,EAAE,OAAO,mBAAM,CAAC,OAAO,UAAU;4BACxC,GAAG,EAAE,mBAAM,CAAC,OAAO;4BACnB,KAAK;4BACL,GAAG;4BACH,OAAO,EAAE,GAAG,IAAI,EAAE,SAAS,EAAE;yBAC9B;qBACF,CAAC;yBACD,KAAK,CAAC,CAAC,CAAC,EAAE;wBACT,eAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;oBAC7B,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;YACP,CAAC;QACH,CAAC;aAAM,IAAI,KAAK,KAAK,oBAAS,CAAC,cAAc,EAAE,CAAC;YAC9C,MAAM,QAAQ,GAAG,kBAAkB,CAAC,mBAAmB,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YACrE,MAAM,GAAG,GAAG,GAAG,mBAAM,CAAC,SAAS,aAAa,QAAQ,EAAE,CAAC;YACvD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAC9B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,KAAK,oBAAS,CAAC,QAAQ,CAC1C,CAAC;gBACF,MAAM,CAAC,OAAO;qBACX,MAAM,CACL,UAAU,CAAC,EAAE,CACX,UAAU,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,CAAC,UAAU,CAAC,UAAU,EAAE,MAAM,CAChE;qBACA,OAAO,CAAC,UAAU,CAAC,EAAE;oBACpB,IAAI,CAAC,aAAa;yBACf,QAAQ,CAAC;wBACR,EAAE,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;wBAC3B,EAAE;wBACF,OAAO,EAAE,UAAU,mBAAM,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,GAC/C,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KACtB,EAAE;wBACF,QAAQ,EAAE,QAAQ;wBAClB,OAAO,EAAE;4BACP,IAAI,EAAE,GAAG,UAAU,CAAC,SAAS,EAAE;4BAC/B,OAAO,EAAE,OAAO,mBAAM,CAAC,OAAO,UAAU;4BACxC,GAAG,EAAE,mBAAM,CAAC,OAAO;4BACnB,KAAK;4BACL,GAAG;4BACH,OAAO,EAAE,GAAG,IAAI,EAAE,SAAS,EAAE;yBAC9B;qBACF,CAAC;yBACD,KAAK,CAAC,CAAC,CAAC,EAAE;wBACT,eAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;oBAC7B,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;YACP,CAAC;QACH,CAAC;aAAM,IAAI,KAAK,KAAK,oBAAS,CAAC,SAAS,EAAE,CAAC;YACzC,MAAM,QAAQ,GAAG,kBAAkB,CAAC,mBAAmB,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YACrE,MAAM,GAAG,GAAG,GAAG,mBAAM,CAAC,SAAS,aAAa,QAAQ,EAAE,CAAC;YACvD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAC9B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,KAAK,oBAAS,CAAC,cAAc,CAChD,CAAC;gBACF,MAAM,CAAC,OAAO;qBACX,MAAM,CACL,UAAU,CAAC,EAAE,CACX,UAAU,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,CAAC,UAAU,CAAC,UAAU,EAAE,MAAM,CAChE;qBACA,OAAO,CAAC,UAAU,CAAC,EAAE;oBACpB,IAAI,CAAC,aAAa;yBACf,QAAQ,CAAC;wBACR,EAAE,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;wBAC3B,EAAE;wBACF,OAAO,EAAE,UAAU,mBAAM,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,GAC/C,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KACtB,EAAE;wBACF,QAAQ,EAAE,QAAQ;wBAClB,OAAO,EAAE;4BACP,IAAI,EAAE,GAAG,UAAU,CAAC,SAAS,EAAE;4BAC/B,OAAO,EAAE,OAAO,mBAAM,CAAC,OAAO,UAAU;4BACxC,GAAG,EAAE,mBAAM,CAAC,OAAO;4BACnB,KAAK;4BACL,GAAG;4BACH,OAAO,EAAE,GAAG,IAAI,EAAE,SAAS,EAAE;yBAC9B;qBACF,CAAC;yBACD,KAAK,CAAC,CAAC,CAAC,EAAE;wBACT,eAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;oBAC7B,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;YACP,CAAC;QACH,CAAC;aAAM,IAAI,KAAK,KAAK,oBAAS,CAAC,QAAQ,EAAE,CAAC;YACxC,OAAO;iBACJ,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC;iBACjC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC;iBACzC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACd,IACE,CAAC,CAAC,IAAI,CAAC,KAAK;oBACZ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC;oBAC7B,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAC7B,CAAC;oBACD,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC3B,CAAC;qBAAM,IACL,CAAC,IAAI,CAAC,KAAK;oBACX,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC;oBAC7B,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAC7B,CAAC;oBACD,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC3B,CAAC;YACH,CAAC,CAAC,CAAC;YACL,KAAK,MAAM,MAAM,IAAI,mBAAM,CAAC,UAAU,EAAE,CAAC;gBACvC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBACzB,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAClB,CAAC;YACH,CAAC;YACD,IAAI,CAAC,aAAa;iBACf,QAAQ,CAAC;gBACR,EAAE;gBACF,EAAE;gBACF,OAAO,EAAE,UAAU,mBAAM,CAAC,OAAO,MAAM,OAAO,CAAC,IAAI,KAAK;gBACxD,QAAQ,EAAE,gBAAgB;gBAC1B,OAAO,EAAE;oBACP,OAAO,EAAE,MAAM,OAAO,CAAC,IAAI,cAAc;oBACzC,KAAK;iBACN;aACF,CAAC;iBACD,KAAK,CAAC,CAAC,CAAC,EAAE;gBACT,eAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC;QACP,CAAC;IACH,CAAC;CACF,CAAA;AA3RY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;qCAGc,sBAAa;QACnB,wBAAO;GAHf,WAAW,CA2RvB"}