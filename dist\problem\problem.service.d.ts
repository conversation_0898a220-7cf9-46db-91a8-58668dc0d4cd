import { Page } from "src/framework/service/IBase.service";
import { QueryRequest } from "src/framework/service/Query";
import { BaseService } from "src/framework/service/base.service";
import { DataSource, Repository, SelectQueryBuilder } from "typeorm";
import { ProblemOperateLog } from "./entities/problem-operate-log.entity";
import { Problem } from "./entities/problem.entity";
export declare class ProblemService extends BaseService<Problem> {
    private logRepository;
    constructor(repository: Repository<Problem>, dataSource: DataSource, logRepository: Repository<ProblemOperateLog>);
    populateQueryBuilder(builder: SelectQueryBuilder<Problem>): void;
    query(request: QueryRequest): Promise<Problem[] | Page<Problem>>;
    assignSkipFields(): string[];
    preSave(entity: Problem): Promise<void>;
    deleteEntities(entities: Problem[], isAdmin: boolean, cb?: (toObsolete: Problem[]) => Promise<void>): Promise<void>;
    delete(ids: number[]): Promise<void>;
}
