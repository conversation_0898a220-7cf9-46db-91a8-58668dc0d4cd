import { FindManyOptions, FindOptionsRelationByString, FindOptionsRelations } from "typeorm";
import { FindOptionsOrder } from "typeorm/find-options/FindOptionsOrder";
import { FindOptionsWhere } from "typeorm/find-options/FindOptionsWhere";
export type OrderRequest = {
    property: string;
    direction?: "ASC" | "DESC";
};
export type PageRequest = {
    no?: number;
    size?: number;
    orders?: OrderRequest[];
};
export type QueryRequest = {
    params?: Record<string, any>;
    operations?: Record<string, any>;
    page?: PageRequest;
};
export declare class Query<T> implements FindManyOptions<T> {
    skip?: number;
    take?: number;
    order?: FindOptionsOrder<T>;
    where?: FindOptionsWhere<T>[] | FindOptionsWhere<T>;
    relations?: FindOptionsRelationByString | FindOptionsRelations<T>;
    private _isPageable;
    constructor(request?: QueryRequest);
    get pageable(): boolean;
}
export interface BatchIdRequest {
    ids: number[];
}
