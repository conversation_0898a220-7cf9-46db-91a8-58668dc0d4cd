"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MailService = void 0;
const mailer_1 = require("@nestjs-modules/mailer");
const common_1 = require("@nestjs/common");
const puppeteer_core_1 = require("puppeteer-core");
const constant_1 = require("../problem/entities/constant");
const properties_1 = require("../utils/properties");
const constant_2 = require("./../problem/entities/constant");
let MailService = class MailService {
    mailerService;
    browser;
    constructor(mailerService, browser) {
        this.mailerService = mailerService;
        this.browser = browser;
    }
    async sendTipEmails(users, type = "audit") {
        const url = `${properties_1.config.carUrl}${type}`;
        const action = type === "task" ? "处理" : "审批";
        users.forEach(item => {
            this.mailerService
                .sendMail({
                to: [item.email],
                cc: [...properties_1.config.testCopyers],
                subject: `QMS通知：【${properties_1.config.appName}】您有${item.problems.size}个问题长时间未${action}`,
                template: "./tip",
                context: {
                    name: `${item.name}`,
                    app: properties_1.config.appName,
                    message: `您有${item.problems.size}个${properties_1.config.appName}问题(${Array.from(item.problems).join(",")})待${action}`,
                    url,
                },
            })
                .catch(e => {
                common_1.Logger.error(e.message, e);
            });
        });
    }
    async sendEmail(reasons, state, problem, tip = false) {
        let image;
        if (this.browser) {
            const page = await this.browser.newPage();
            await page.setViewport({ width: 1920, height: 9999 });
            try {
                await page.goto(`${properties_1.config.carUrl}?authKey=1&redirect=/pdf/${problem.id}`, {
                    waitUntil: "domcontentloaded",
                });
                await page.waitForNavigation({ timeout: 20000 });
                await page.waitForNetworkIdle({ timeout: 20000 });
                await page.content();
                const bodyHandler = await page.$("body");
                const { width, height } = await bodyHandler.boundingBox();
                await page.setViewport({
                    width: Math.ceil(width),
                    height: Math.ceil(height),
                    deviceScaleFactor: 2,
                });
                image = await page.screenshot({
                    fullPage: true,
                    encoding: "base64",
                });
            }
            finally {
                await page.close();
            }
        }
        else {
            common_1.Logger.warn('Browser not available, skipping screenshot generation');
            image = null;
        }
        const to = [];
        const cc = [...properties_1.config.testCopyers];
        if (state === constant_1.NodeState.ANALYZE) {
            const redirect = encodeURIComponent(`/task?entityId=${problem.id}`);
            const url = `${properties_1.config.loginPage}&redirect=${redirect}`;
            for (const reason of reasons) {
                const action = reason.status === constant_2.ReasonStatus.REJECTED
                    ? "被驳回"
                    : "需要原因分析和行动计划";
                reason.configs
                    .filter(nodeConfig => nodeConfig.state === state && !!nodeConfig.ownerEmail?.length)
                    .forEach(nodeConfig => {
                    this.mailerService
                        .sendMail({
                        to: [nodeConfig.ownerEmail],
                        cc,
                        subject: `QMS通知：【${properties_1.config.appName}】${problem.code}待处理`,
                        template: "./task",
                        context: {
                            name: `${nodeConfig.ownerName}`,
                            app: properties_1.config.appName,
                            message: `您有一个${properties_1.config.appName}问题${action}`,
                            image,
                            url,
                            applier: problem.creatorName,
                        },
                    })
                        .catch(e => {
                        common_1.Logger.error(e.message, e);
                    });
                });
            }
        }
        else if (state === constant_1.NodeState.VALIDATE) {
            const redirect = encodeURIComponent(`/task?entityId=${problem.id}`);
            const url = `${properties_1.config.loginPage}&redirect=${redirect}`;
            for (const reason of reasons) {
                const node = reason.configs.find(node => node.state === constant_1.NodeState.ANALYZE);
                reason.configs
                    .filter(nodeConfig => nodeConfig.state === state && !!nodeConfig.ownerEmail?.length)
                    .forEach(nodeConfig => {
                    this.mailerService
                        .sendMail({
                        to: [nodeConfig.ownerEmail],
                        cc,
                        subject: `QMS通知：【${properties_1.config.appName}】${problem.code}待处理`,
                        template: "./task",
                        context: {
                            name: `${nodeConfig.ownerName}`,
                            message: `您有一个${properties_1.config.appName}问题需要进行效果验证`,
                            app: properties_1.config.appName,
                            image,
                            url,
                            applier: `${node?.ownerName}`,
                        },
                    })
                        .catch(e => {
                        common_1.Logger.error(e.message, e);
                    });
                });
            }
        }
        else if (state === constant_1.NodeState.ANALYZE_AUDIT) {
            const redirect = encodeURIComponent(`/audit?entityId=${problem.id}`);
            const url = `${properties_1.config.loginPage}&redirect=${redirect}`;
            for (const reason of reasons) {
                const node = reason.configs.find(node => node.state === constant_1.NodeState.ANALYZE);
                reason.configs
                    .filter(nodeConfig => nodeConfig.state === state && !!nodeConfig.ownerEmail?.length)
                    .forEach(nodeConfig => {
                    this.mailerService
                        .sendMail({
                        to: [nodeConfig.ownerEmail],
                        cc,
                        subject: `QMS通知：【${properties_1.config.appName}】${problem.code}${tip ? "已超期,请及时审批" : "待审批"}`,
                        template: "./task",
                        context: {
                            name: `${nodeConfig.ownerName}`,
                            message: `您有一个${properties_1.config.appName}问题需要进行审批`,
                            app: properties_1.config.appName,
                            image,
                            url,
                            applier: `${node?.ownerName}`,
                        },
                    })
                        .catch(e => {
                        common_1.Logger.error(e.message, e);
                    });
                });
            }
        }
        else if (state === constant_1.NodeState.VALIDATE_AUDIT) {
            const redirect = encodeURIComponent(`/audit?entityId=${problem.id}`);
            const url = `${properties_1.config.loginPage}&redirect=${redirect}`;
            for (const reason of reasons) {
                const node = reason.configs.find(node => node.state === constant_1.NodeState.VALIDATE);
                reason.configs
                    .filter(nodeConfig => nodeConfig.state === state && !!nodeConfig.ownerEmail?.length)
                    .forEach(nodeConfig => {
                    this.mailerService
                        .sendMail({
                        to: [nodeConfig.ownerEmail],
                        cc,
                        subject: `QMS通知：【${properties_1.config.appName}】${problem.code}${tip ? "已超期,请及时审批" : "待审批"}`,
                        template: "./task",
                        context: {
                            name: `${nodeConfig.ownerName}`,
                            message: `您有一个${properties_1.config.appName}问题需要进行审批`,
                            app: properties_1.config.appName,
                            image,
                            url,
                            applier: `${node?.ownerName}`,
                        },
                    })
                        .catch(e => {
                        common_1.Logger.error(e.message, e);
                    });
                });
            }
        }
        else if (state === constant_1.NodeState.CQE_AUDIT) {
            const redirect = encodeURIComponent(`/audit?entityId=${problem.id}`);
            const url = `${properties_1.config.loginPage}&redirect=${redirect}`;
            for (const reason of reasons) {
                const node = reason.configs.find(node => node.state === constant_1.NodeState.VALIDATE_AUDIT);
                reason.configs
                    .filter(nodeConfig => nodeConfig.state === state && !!nodeConfig.ownerEmail?.length)
                    .forEach(nodeConfig => {
                    this.mailerService
                        .sendMail({
                        to: [nodeConfig.ownerEmail],
                        cc,
                        subject: `QMS通知：【${properties_1.config.appName}】${problem.code}${tip ? "已超期,请及时审批" : "待审批"}`,
                        template: "./task",
                        context: {
                            name: `${nodeConfig.ownerName}`,
                            message: `您有一个${properties_1.config.appName}问题需要进行审批`,
                            app: properties_1.config.appName,
                            image,
                            url,
                            applier: `${node?.ownerName}`,
                        },
                    })
                        .catch(e => {
                        common_1.Logger.error(e.message, e);
                    });
                });
            }
        }
        else if (state === constant_1.NodeState.COMPLETE) {
            reasons
                .flatMap(reason => reason.configs)
                .filter(node => !!node.ownerEmail?.length)
                .forEach(node => {
                if (!!node.state &&
                    !to.includes(node.ownerEmail) &&
                    !cc.includes(node.ownerEmail)) {
                    to.push(node.ownerEmail);
                }
                else if (!node.state &&
                    !to.includes(node.ownerEmail) &&
                    !cc.includes(node.ownerEmail)) {
                    cc.push(node.ownerEmail);
                }
            });
            for (const copyer of properties_1.config.completeCc) {
                if (!cc.includes(copyer)) {
                    cc.push(copyer);
                }
            }
            this.mailerService
                .sendMail({
                to,
                cc,
                subject: `QMS通知：【${properties_1.config.appName}】问题${problem.code}已关闭`,
                template: "./notification",
                context: {
                    message: `问题(${problem.code})已处理完成, 特此通知`,
                    image,
                },
            })
                .catch(e => {
                common_1.Logger.error(e.message, e);
            });
        }
    }
};
exports.MailService = MailService;
exports.MailService = MailService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Optional)()),
    __param(1, (0, common_1.Inject)('Browser')),
    __metadata("design:paramtypes", [mailer_1.MailerService,
        puppeteer_core_1.Browser])
], MailService);
//# sourceMappingURL=mail.service.js.map