{"name": "@fastify/fast-json-stringify-compiler", "description": "Build and manage the fast-json-stringify instances for the fastify framework", "version": "4.3.0", "main": "index.js", "types": "types/index.d.ts", "scripts": {"lint": "standard", "lint:fix": "standard --fix", "unit": "tap test/**/*.test.js", "test": "npm run unit && npm run test:typescript", "test:typescript": "tsd"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fast-json-stringify-compiler.git"}, "keywords": [], "author": "<PERSON> <<EMAIL>> (https://github.com/Eomm)", "license": "MIT", "bugs": {"url": "https://github.com/fastify/fast-json-stringify-compiler/issues"}, "homepage": "https://github.com/fastify/fast-json-stringify-compiler#readme", "devDependencies": {"@fastify/pre-commit": "^2.0.2", "fastify": "^4.0.0", "sanitize-filename": "^1.6.3", "standard": "^17.0.0", "tap": "^16.0.0", "tsd": "^0.28.0"}, "pre-commit": ["lint", "test"], "dependencies": {"fast-json-stringify": "^5.7.0"}}