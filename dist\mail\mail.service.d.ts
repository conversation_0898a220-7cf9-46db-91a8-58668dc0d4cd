import { MailerService } from "@nestjs-modules/mailer";
import { <PERSON><PERSON><PERSON> } from "puppeteer-core";
import { NodeState } from "src/problem/entities/constant";
import { Problem } from "src/problem/entities/problem.entity";
import { Reason } from "src/problem/entities/reason.entity";
export declare class MailService {
    private mailerService;
    private browser;
    constructor(mailerService: MailerService, browser: Browser);
    sendTipEmails(users: {
        name: string;
        email: string;
        problems: Set<string>;
    }[], type?: "task" | "audit"): Promise<void>;
    sendEmail(reasons: Reason[], state: NodeState, problem: Problem, tip?: boolean): Promise<void>;
}
