import { Payload } from "src/auth/jwt.strategy";
import { QueryRequest } from "src/framework/service/Query";
import { ProblemStatus } from "./../problem/entities/constant";
import { ProblemService } from "./../problem/problem.service";
import { ReasonService } from "./../problem/reason.service";
import { DashboardService } from "./dashboard.service";
export declare class DashboardController {
    private readonly dashboardService;
    private readonly problemService;
    private readonly reasonService;
    constructor(dashboardService: DashboardService, problemService: ProblemService, reasonService: ReasonService);
    panel(): Promise<{
        open: number;
        follow: number;
    }>;
    processing(req: {
        payload: Payload;
    }): Promise<number>;
    private getTaskCount;
    private getAuditingCount;
    todo(req: {
        payload: Payload;
    }): Promise<{
        task: number;
        audit: number;
    }>;
    panelData(status: ProblemStatus): Promise<import("../framework/service/IBase.service").Page<import("../problem/entities/problem.entity").Problem> | import("../problem/entities/problem.entity").Problem[]>;
    processingData(req: {
        payload: Payload;
    }): Promise<import("../framework/service/IBase.service").Page<import("../problem/entities/problem.entity").Problem> | import("../problem/entities/problem.entity").Problem[]>;
    getBaseBuilder(request: QueryRequest, skip?: boolean): import("typeorm").SelectQueryBuilder<import("../problem/entities/problem.entity").Problem>;
    stepChart(pie: "1" | "0", request: QueryRequest): Promise<{
        data: any[];
        total: number;
    }>;
    customerChart(pie: "1" | "0", request: QueryRequest): Promise<{
        data: any[];
        total: number;
    }>;
    unqualityTypeChart(pie: "1" | "0", request: QueryRequest): Promise<{
        data: any[];
        total: number;
    }>;
    reasonCategoryChart(request: QueryRequest): Promise<{
        data: any[];
    }>;
    reasonSubCategoryChart(category: string, request: QueryRequest): Promise<{
        data: any[];
    }>;
    chartData(request: QueryRequest): Promise<import("../problem/entities/problem.entity").Problem[]>;
}
