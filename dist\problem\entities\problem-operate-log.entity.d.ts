import { NumberIdTimeObject } from "../../framework/model/base";
import { Relation } from "typeorm";
import { Problem } from "./problem.entity";
import { Reason } from "./reason.entity";
export declare class ProblemOperateLog extends NumberIdTimeObject {
    problem: Relation<Problem>;
    reason?: Relation<Reason>;
    operatorId: number;
    operatorName: string;
    action: {
        key: string;
        message: string;
        params: Record<string, string>;
    };
    before: Record<string, any>;
    after: Record<string, any>;
}
