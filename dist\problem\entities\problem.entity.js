"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Problem = void 0;
const typeorm_1 = require("typeorm");
const base_1 = require("../../framework/model/base");
const constant_1 = require("./constant");
const problem_operate_log_entity_1 = require("./problem-operate-log.entity");
const reason_entity_1 = require("./reason.entity");
let Problem = class Problem extends base_1.NumberIdTimeObject {
    code;
    createdOn;
    machineCategory;
    machineType;
    customer;
    projectCode;
    productLine;
    factory;
    businessUnit;
    creatorId;
    creatorName;
    productStep;
    status;
    description;
    descriptions;
    goodPartImages;
    badPartImages;
    workOrderCode;
    workOrderNum;
    reasons;
    logs;
    cqeId;
    cqeName;
    todoId;
};
exports.Problem = Problem;
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 100 }),
    __metadata("design:type", String)
], Problem.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: "date" }),
    __metadata("design:type", Date)
], Problem.prototype, "createdOn", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "机种" }),
    __metadata("design:type", String)
], Problem.prototype, "machineCategory", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "机型" }),
    __metadata("design:type", String)
], Problem.prototype, "machineType", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "客户" }),
    __metadata("design:type", String)
], Problem.prototype, "customer", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "项目编号" }),
    __metadata("design:type", String)
], Problem.prototype, "projectCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "生产线" }),
    __metadata("design:type", String)
], Problem.prototype, "productLine", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "工厂" }),
    __metadata("design:type", String)
], Problem.prototype, "factory", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "事业部" }),
    __metadata("design:type", String)
], Problem.prototype, "businessUnit", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "创建人ID" }),
    __metadata("design:type", Number)
], Problem.prototype, "creatorId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "创建人姓名" }),
    __metadata("design:type", String)
], Problem.prototype, "creatorName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "产品阶段" }),
    __metadata("design:type", String)
], Problem.prototype, "productStep", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: constant_1.ProblemStatus,
        default: constant_1.ProblemStatus.DRAFT,
        comment: "项目状态",
    }),
    __metadata("design:type", String)
], Problem.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "问题描述", type: "text" }),
    __metadata("design:type", String)
], Problem.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "问题描述(5w2h)", type: "json" }),
    __metadata("design:type", Object)
], Problem.prototype, "descriptions", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: "json", comment: "好件附件" }),
    __metadata("design:type", Array)
], Problem.prototype, "goodPartImages", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: "json", comment: "坏件附件" }),
    __metadata("design:type", Array)
], Problem.prototype, "badPartImages", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "工单号" }),
    __metadata("design:type", String)
], Problem.prototype, "workOrderCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "工单数量" }),
    __metadata("design:type", Number)
], Problem.prototype, "workOrderNum", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => reason_entity_1.Reason, reason => reason.problem, {
        cascade: true,
        orphanedRowAction: "delete",
    }),
    __metadata("design:type", Array)
], Problem.prototype, "reasons", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => problem_operate_log_entity_1.ProblemOperateLog, log => log.problem, {
        cascade: true,
        orphanedRowAction: "delete",
    }),
    __metadata("design:type", Array)
], Problem.prototype, "logs", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "CQE ID" }),
    __metadata("design:type", Number)
], Problem.prototype, "cqeId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "CQE 姓名" }),
    __metadata("design:type", String)
], Problem.prototype, "cqeName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, comment: "CQE TODO ID" }),
    __metadata("design:type", Number)
], Problem.prototype, "todoId", void 0);
exports.Problem = Problem = __decorate([
    (0, typeorm_1.Entity)()
], Problem);
//# sourceMappingURL=problem.entity.js.map