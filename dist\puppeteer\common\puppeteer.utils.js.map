{"version": 3, "file": "puppeteer.utils.js", "sourceRoot": "", "sources": ["../../../src/puppeteer/common/puppeteer.utils.ts"], "names": [], "mappings": ";;AAKA,0CAUC;AAED,oCAMC;AAED,4CAUC;AAlCD,mDAA+C;AAE/C,gEAA8D;AAE9D,SAAgB,eAAe,CAC7B,UAA2C,0CAAoB;IAE/D,OAAO,0CAAoB,KAAK,OAAO;QACrC,CAAC,CAAC,wBAAO;QACT,CAAC,CAAC,QAAQ,KAAK,OAAO,OAAO;YAC3B,CAAC,CAAC,GAAG,OAAO,SAAS;YACrB,CAAC,CAAC,0CAAoB,KAAK,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI;gBACtD,CAAC,CAAC,wBAAO;gBACT,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,SAAS,CAAC;AACnC,CAAC;AAED,SAAgB,YAAY,CAC1B,IAAY,EACZ,UAA2C,0CAAoB;IAE/D,MAAM,aAAa,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAChD,OAAO,GAAG,aAAa,GAAG,IAAI,MAAM,CAAC;AACvC,CAAC;AAED,SAAgB,gBAAgB,CAC9B,UAA2C,0CAAoB;IAE/D,OAAO,0CAAoB,KAAK,OAAO;QACrC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,QAAQ,KAAK,OAAO,OAAO;YAC3B,CAAC,CAAC,GAAG,OAAO,GAAG;YACf,CAAC,CAAC,0CAAoB,KAAK,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI;gBACtD,CAAC,CAAC,EAAE;gBACJ,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,CAAC;AAC7B,CAAC"}