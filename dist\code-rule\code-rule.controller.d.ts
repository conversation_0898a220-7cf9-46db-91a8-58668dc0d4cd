import { CodeRuleService } from "./code-rule.service";
import type { BatchIdRequest, QueryRequest } from "../framework/service/Query";
import { CodeRule } from "./entities/code-rule.entity";
export declare class CodeRuleController {
    private readonly service;
    constructor(service: CodeRuleService);
    query(request: QueryRequest): Promise<CodeRule[] | import("../framework/service/IBase.service").Page<CodeRule>>;
    save(entity: Partial<CodeRule>): Promise<CodeRule>;
    remove(request: BatchIdRequest): Promise<void>;
    duplicate(entity: Partial<CodeRule>): Promise<boolean>;
}
