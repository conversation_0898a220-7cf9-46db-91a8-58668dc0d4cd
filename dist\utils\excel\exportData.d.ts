import ExcelJS, { Borders, Cell, Style } from "exceljs";
import type { Response } from "express";
export type Header = {
    header: string;
    key: string;
}[];
export declare function getDefaultHeaderCellStyle(): Partial<Style>;
export declare function getDefaultCellStyle(): Partial<Style>;
export declare function countChineseCharacters(str: string): number;
export declare function getDefaultBorder(): Partial<Borders>;
export default class Exporter {
    private readonly _workbook;
    constructor(props?: {
        creator?: string;
        lastModifiedBy?: string;
        created?: Date;
        modified?: Date;
    });
    addSheet<T>(options: {
        name?: string;
        header?: Header;
        data?: T[];
        setCellStyle?: (cell: Cell, rowNum: number, colNum: number) => Promise<any> | any;
        autoFit?: boolean;
    }): ExcelJS.Worksheet;
    doExport(target: string | {
        filename: string;
        response: Response;
    }): Promise<void>;
    get workbook(): ExcelJS.Workbook;
}
